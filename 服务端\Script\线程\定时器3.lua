
local ffi = require("ffi")
local class = class  -- 假设 'class' 是全局可用的
local ggethread = require("ggethread")
local os_time = os.time
local os_date = os.date
local table_insert = table.insert
local table_remove = table.remove
local table_unpack = table.unpack
local tonumber = tonumber
local pairs = pairs
local print = print
local collectgarbage = collectgarbage

local 数据数量 = ffi.new("int[1]", 0)
local 线程发送数据 = {}
local 定时时间 = os_time()
local fenzhong = tonumber(os_date("%M", 定时时间))
local lastGCTime = 定时时间
local lastRankTime = 定时时间

local 保存任务步骤 = 1  -- 保存任务步骤计数器
local 正在保存 = false

local 定时器 = class()

-- 添加新的状态变量
local 存档队列 = {}  -- 存储待保存的玩家ID
local 每批处理数量 = 20  -- 每次处理的玩家数量
local 存档间隔 = 1  -- 批次间隔(秒)
local 上次存档时间 = 0
local 存档倒计时 = 15  -- 添加15秒倒计时变量
local 上次倒计时提示 = 0  -- 上次提示时间

local 服务器启动时间 = os_time()
local 启动保护时间 = 300  -- 5分钟保护时间

-- 添加新变量来控制保存步骤的间隔
local 保存步骤间隔 = 2  -- 每个保存步骤之间间隔2秒
local 上次保存步骤时间 = 0

function 定时器:初始化(v, 循环文件)
    self.线程 = ggethread(循环文件)
    
    -- 捕获定时器实例
    local 定时器_instance = self

    self.线程.消息返回 = function(_, msg, ...)
        if msg == "取数量指针" then
            return tonumber(ffi.cast("intptr_t", 数据数量))
        elseif msg == "取数据" then
            if #线程发送数据 > 0 then
                local r = table_remove(线程发送数据, 1)
                return table_unpack(r)
            end
        elseif msg == "循环更新" then
            定时器_instance:循环更新()
        end
    end

    self:启动(v)

    -- 设置垃圾回收参数
    collectgarbage("setpause", 200)
    collectgarbage("setstepmul", 500)

    -- 初始化首次执行时间
    lastGCTime = os_time()
    lastRankTime = os_time()
end

function 定时器:启动(v)
    self.线程:启动(v)
    self.线程:置延迟(v)
end

function 定时器:发送(...)
    数据数量[0] = 数据数量[0] + 1
    table_insert(线程发送数据, { ... })
end

function 定时器:循环更新()
    local currentTime = os_time()
    local currentMinute = tonumber(os_date("%M", currentTime))
    local currentSecond = tonumber(os_date("%S", currentTime))

    -- 添加启动保护检查
    if currentTime - 服务器启动时间 < 启动保护时间 then
        return
    end

    -- 处理分批存档
    if #存档队列 > 0 and currentTime - 上次存档时间 >= 存档间隔 then
        上次存档时间 = currentTime
    end

    -- 整点触发存档（每小时一次，仅在未在保存状态时触发）
    if currentMinute == 0 and currentSecond == 0 and not 正在保存 and 存档倒计时 <= 0 then
        if not 服务端参数.玩家存档 or 服务端参数.玩家存档 < currentTime then
            存档倒计时 = 15
            上次倒计时提示 = currentTime
             print("系统将在15秒后进行存档")
            return
        end
    end

    -- 处理倒计时提示
    if 存档倒计时 > 0 then
        if currentTime - 上次倒计时提示 >= 1 then
            存档倒计时 = 存档倒计时 - 1
            if 存档倒计时 > 0 and 存档倒计时 % 5 == 0 then
                广播消息(string.format("系统将在%d秒后进行存档", 存档倒计时))
            elseif 存档倒计时 == 0 then
                正在保存 = true
                self:开始系统存档()
                 print("系统开始进行存档...")
            end
            上次倒计时提示 = currentTime
        end
    end

    -- 优化保存步骤检查
    if 正在保存 and 保存任务步骤 >= 1 and 保存任务步骤 <= 3 then
        if currentTime - 上次保存步骤时间 >= 保存步骤间隔 then
            self:保存系统数据步骤()
            上次保存步骤时间 = currentTime
        end
    end

    -- 处理玩家在线时间和召唤兽临时属性
    if fenzhong ~= currentMinute and currentSecond == 0 then
        fenzhong = currentMinute
        for n, v in pairs(玩家数据) do
            if v then
                v.角色:增加在线时间()
                if currentMinute == 55 and v.召唤兽 then
                    v.召唤兽:检查临时属性()
                end
            end
        end
    end

    -- 调整神兵异兽榜的统计间隔（例如每4小时执行一次）
    if currentTime - lastRankTime >= 4 * 60 * 60 then
        神兵异兽榜:初始化统计()
        lastRankTime = currentTime
    end

    -- 刷新珍品和神器物品，并触发保存系统数据
    if currentSecond == 0 and (currentMinute == 1 or currentMinute == 30) then
        商店处理类:刷新珍品()
        商店处理类:刷新神器物品()
    end
end

function 定时器:保存系统数据步骤()
    if 保存任务步骤 == 1 then
        -- 检查是否有错误日志需要保存
        if 错误数目 and 错误数目 > 0 then
            __S服务:输出("定时保存检测到 " .. 错误数目 .. " 条错误日志，开始保存...")
        end
        
        -- 保存系统数据（包括错误日志）
        保存系统数据()
        保存任务步骤 = 2
    elseif 保存任务步骤 == 2 then
        保存成就()
        保存任务步骤 = 3
    elseif 保存任务步骤 == 3 then
        神兵异兽榜:存档()
        保存任务步骤 = 1
        正在保存 = false
        上次保存步骤时间 = 0  -- 重置时间
        local 耗时 = os_time() - 服务端参数.玩家存档
        __S服务:输出(string.format("系统数据存档完成，总耗时：%d秒", 耗时))
    end
end

function 定时器:开始系统存档()
    服务端参数.玩家存档 = os_time()
    存档队列 = {}
    
    -- 收集需要存档的玩家ID
    local 在线人数 = 0
    for n, v in pairs(玩家数据) do
        if v and v.角色 then
            table.insert(存档队列, n)
            在线人数 = 在线人数 + 1
        end
    end
    
    -- 开始第一批存档
    self:处理存档队列()
end

function 定时器:处理存档队列()
    if #存档队列 == 0 then
        保存任务步骤 = 1
        self:保存系统数据步骤()
        return
    end

    -- 处理一批玩家
    local 本批玩家 = {}
    for i = 1, math.min(每批处理数量, #存档队列) do
        local 玩家id = table.remove(存档队列, 1)
        if 玩家id then  -- 确保玩家ID有效
            table.insert(本批玩家, 玩家id)
        end
    end
    
    -- 只有当本批玩家不为空时才保存
    if #本批玩家 > 0 then
        保存玩家数据(本批玩家)
    else
        __S服务:输出("警告：定时器存档队列中没有有效的玩家ID")
    end
end

return 定时器