-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:43
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-07-26 22:11:02

function 保存系统数据()
    -- 系统数据存档已经由定时器分批处理
    -- 这里只处理帮派和错误日志等必要数据

    -- 预处理帮派数据
    local 帮派预处理 = {}
    for k, v in pairs(取所有帮派) do
        if not v.已解散 then
            帮派预处理[k] = table.tostring(帮派数据[k])
        end
    end

    -- 异步保存帮派数据
    保存帮派数据(帮派预处理)

    -- 处理错误日志
    if 错误数目 and 错误数目 > 0 then
        local 当前时间 = os.time()
        local 文件名称 = os.date("%Y-%m-%d-%H时%M分%S秒", 当前时间)

        -- 确保错误日志存在且非nil
        if 错误日志 == nil then
            错误日志 = {}
            __S服务:输出("警告：错误日志为nil，已初始化为空表")
        end

        -- 处理错误日志兼容性问题
        local 错误内容 = "本次错误记录数：" .. 错误数目 .. "#换行符"

        -- 调试输出
        __S服务:输出("正在处理错误日志，类型: " .. type(错误日志) .. ", 错误数: " .. 错误数目)

        -- 更安全地处理错误日志，支持表和字符串两种类型
        if type(错误日志) == "table" then
            -- 如果错误日志是表，将其转换为字符串
            for i, 日志 in ipairs(错误日志) do
                if 日志 then -- 确保日志条目不是nil
                    错误内容 = 错误内容 .. tostring(日志) .. "#换行符"
                end
            end
        elseif type(错误日志) == "string" then
            -- 如果错误日志是字符串，直接使用
            错误内容 = 错误内容 .. 错误日志
        else
            -- 其他情况，转换为字符串
            错误内容 = 错误内容 .. tostring(错误日志)
        end

        -- 创建log目录
        local log目录 = "log"
        if not f函数.文件是否存在(log目录) then
            -- 尝试创建目录并验证
            __S服务:输出("正在创建日志目录: " .. log目录)
            os.execute("mkdir " .. log目录)

            -- 验证目录是否创建成功
            if not f函数.文件是否存在(log目录) then
                __S服务:输出("警告：创建日志目录失败，尝试使用完整路径")
                log目录 = 程序目录 .. "/log"
                os.execute("mkdir " .. log目录)
            end
        end

        -- 安全写出文件
        local 日志文件路径 = log目录 .. "/" .. 文件名称 .. ".txt"
        local 写入结果 = pcall(写出文件, 日志文件路径, 错误内容)

        if 写入结果 then
            __S服务:输出("错误日志已保存至: " .. 日志文件路径)
        else
            -- 尝试在当前目录写入
            __S服务:输出("警告：写入日志失败，尝试写入当前目录")
            写出文件(文件名称 .. "_error.txt", 错误内容)
        end

        -- 重置错误日志
        if type(错误日志) == "table" then
            错误日志 = {}
        else
            错误日志 = ""
        end
        错误数目 = 0
    end

    -- 更新服务器标题
    local 周数 = tonumber(os.date("%w", os.time()))
    local 当前时间 = 时间转换(os.time())
    __S服务:置标题("端口[" .. 服务端参数.端口 .. "]   版本[" .. 服务端参数.版本 .. "]   保存时间:[周" .. 周数 .. " " .. 当前时间 .. "]")
end

function 保存玩家数据(待保存玩家)
    -- 检查参数是否有效
    if not 待保存玩家 or type(待保存玩家) ~= "table" then
        __S服务:输出("警告：保存玩家数据时参数无效，跳过保存")
        return
    end

    -- 优化内存管理
    if 玩家异步保存次数 == nil then
        玩家异步保存次数 = 0
    end

    -- 保存玩家数据
    for _, 玩家ID in ipairs(待保存玩家) do
        if 玩家数据[玩家ID] then
            玩家数据[玩家ID].角色:存档()
        end
    end

    玩家异步保存次数 = 玩家异步保存次数 + 1

    if 玩家异步保存次数 >= 4 then
        玩家异步保存次数 = 0
        collectgarbage("step", 100)  -- 使用step代替collect,减少卡顿
    end
end

-- 新增：专门用于保存任务数据的函数
-- 添加防重复保存机制
local 上次任务保存时间 = 0
local 任务保存间隔 = 5  -- 5秒内不重复保存

function 保存任务数据()
    -- 防止频繁保存，减少文件IO
    local 当前时间 = os.time()
    if 当前时间 - 上次任务保存时间 < 任务保存间隔 then
        return  -- 跳过本次保存
    end

    -- 预处理任务数据
    local llsj = {}
    for n, v in pairs(任务数据) do
        if v.是存档 then
            local 任务存档 = table.loadstring(table.tostring(任务数据[n]))
            任务存档.存储id = n
            table.insert(llsj, 任务存档)
        end
    end

    -- 保存任务数据到文件
    写出文件("游戏数据/任务数据.txt", table.tostring(llsj))
    上次任务保存时间 = 当前时间
    collectgarbage("step", 5)  -- 小规模GC
end

function 保存帮派数据(帮派预处理)
    -- 增加保存日志
   -- __S服务:输出("正在保存双倍数据，文件大小："..string.len(table.tostring(双倍数据)))
    写出文件("游戏数据/双倍数据.txt", table.tostring(双倍数据))

    -- 调用任务数据保存函数
    保存任务数据()

    -- 将所有需要保存的数据加入队列（移除任务数据，因为已单独处理）
    local 存档表 = {
        {路径 = "游戏数据/科举排行数据.txt", 数据 = 科举排行数据},
        {路径 = "游戏数据/首席数据.txt", 数据 = 首席资源数据},
        {路径 = "游戏数据/红包记录.txt", 数据 = 红包记录},
        {路径 = "游戏数据/仙玉数据.txt", 数据 = 仙玉数据},
        {路径 = "游戏数据/商会数据.txt", 数据 = 商会数据},
        {路径 = "游戏数据/物品店数据.txt", 数据 = 物品店数据},
        {路径 = "游戏数据/唤兽店数据.txt", 数据 = 唤兽店数据},
        {路径 = "游戏数据/古玩数据.txt", 数据 = 古玩数据},
        {路径 = "游戏数据/拍卖系统.txt", 数据 = 拍卖系统数据},
        {路径 = "游戏数据/藏宝阁数据.txt", 数据 = 藏宝阁数据},
        {路径 = "游戏数据/寄存数据.txt", 数据 = 寄存数据},
        {路径 = "游戏数据/房屋数据.txt", 数据 = 房屋数据},
        {路径 = "游戏数据/师徒数据.txt", 数据 = 师徒数据},
        {路径 = "游戏数据/雪人活动.txt", 数据 = 雪人活动},
    }

    -- 异步处理存档队列
    for _, v in ipairs(存档表) do
        local 数据 = table.tostring(v.数据)
        写出文件(v.路径, 数据)
        collectgarbage("step", 10)  -- 每次写文件后进行小规模GC
    end

    -- 处理帮派数据
    for k, v in pairs(帮派预处理) do
        写出文件("游戏数据/帮派/帮派数据"..k..".txt", v)
        collectgarbage("step", 10)  -- 减少每次GC的工作量（从10改为5）
    end
end

function 写出封禁记录(账号,数字id,ip)
	封禁记录[账号]={id=数字id,ip=ip}
	写出文件([[WPE/封禁记录.txt]],table.tostring(封禁记录))
	__S服务:输出("写出封禁记录成功")
end

function 强制下线()
	for n, v in pairs(战斗准备类.战斗盒子) do
		if 战斗准备类.战斗盒子[n]~=nil  then
			战斗准备类.战斗盒子[n]:结束战斗(0,0,1,1)
		end
	end
	for n, v in pairs(玩家数据) do
		if 玩家数据[n]~=nil and 玩家数据[n].连接id ~= "假人" then
			玩家数据[n].角色:存档()
			系统处理类:断开游戏(n)
		end
	end
	-- 强制下线后确保任务数据被保存
	保存任务数据()
end

function 打印在线时间()
	local 语句=""
	for n, v in pairs(在线时间) do
		语句=语句..string.format("角色id：%s，本日累积在线：%s秒#换行符",n,在线时间[n])
	end
	写出文件("在线时间.txt",语句)
end

function 异常账号(数字id,信息)
	__S服务:输出(信息)
end

function 查看在线列表()
	local 列表=""
	for n, v in pairs(玩家数据) do
		列表=列表..format("账号%s,角色id%s",玩家数据[n].账号,n)..'#换行符'..'#换行符'
	end
	写出文件("在线列表.txt",列表)
end

-- 在最后添加测试错误日志功能
function 测试错误日志()
    -- 添加一个测试错误
    local 测试错误 = "这是一个测试错误 - " .. os.date("%Y-%m-%d %H:%M:%S")
    __S服务:输出("添加测试错误: " .. 测试错误)

    -- 检查错误日志状态
    __S服务:输出("当前错误日志类型: " .. type(错误日志))
    __S服务:输出("当前错误数目: " .. tostring(错误数目 or "nil"))

    if type(错误日志) == "table" then
        table.insert(错误日志, 测试错误)
    else
        错误日志 = 错误日志 .. "\n" .. 测试错误
    end

    -- 增加错误计数
    错误数目 = (错误数目 or 0) + 1

    -- 测试保存功能
    保存系统数据()

    -- 返回状态
    return {
        错误日志类型 = type(错误日志),
        错误数目 = 错误数目,
        测试错误 = 测试错误
    }
end