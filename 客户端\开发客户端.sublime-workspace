{"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 4495, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[7, 1, "revert", null, "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", "GAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAANgCAAAAAAAA2AIAAAAAAAAAAAAAAADwvw"], [10, 1, "revert", null, "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", "GAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAMECAAAAAAAAwQIAAAAAAAAAAAAAAADwvw"], [11, 1, "insert", {"characters": "4"}, "AgAAACMCAAAAAAAAJAIAAAAAAAAAAAAAJAIAAAAAAAAkAgAAAAAAAAEAAAAz", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAIwIAAAAAAAAkAgAAAAAAAAAAAAAAAPC/"], [13, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-07-26 22:14:49"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDctMjAgMDI6MTI6NDQ", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAJAIAAAAAAAAkAgAAAAAAAAAAAAAAAPC/"], [14, 1, "left_delete", null, "AQAAACMCAAAAAAAAIwIAAAAAAAABAAAANA", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAJAIAAAAAAAAkAgAAAAAAAAAAAAAAAPC/"], [15, 1, "insert", {"characters": "3"}, "AQAAACMCAAAAAAAAJAIAAAAAAAAAAAAA", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAIwIAAAAAAAAjAgAAAAAAAAAAAAAAAPC/"], [17, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-07-26 22:15:07"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDctMjYgMjI6MTQ6NDk", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAJAIAAAAAAAAkAgAAAAAAAAAAAAAAAPC/"], [23, 1, "insert", {"characters": "4"}, "AgAAACMCAAAAAAAAJAIAAAAAAAAAAAAAJAIAAAAAAAAkAgAAAAAAAAEAAAAz", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAIwIAAAAAAAAkAgAAAAAAAAAAAAAAAPC/"], [25, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-07-26 23:00:55"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDctMjYgMjI6MTU6MDc", "FgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAJAIAAAAAAAAkAgAAAAAAAAAAAAAAAPC/"], [3, 1, "toggle_comment", {"block": false}, "AwAAADsLAAAAAAAAOwsAAAAAAAADAAAALS0gFAsAAAAAAAAUCwAAAAAAAAMAAAAtLSD0CgAAAAAAAPQKAAAAAAAAAwAAAC0tIA", "FAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABBCwAAAAAAAPQKAAAAAAAAAAAAAAAA8L8"], [10, 2, "left_delete", null, "AgAAAN0KAAAAAAAA3QoAAAAAAAA2AAAALS0g5Li05pe256aB55So5aSa5byA5ZmoRExM5Yqg6L295Lul6Kej5Yaz5ZCv5Yqo6Zeu6aKY3AoAAAAAAADcCgAAAAAAAAEAAAAK", "FAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADzCgAAAAAAAN0KAAAAAAAAAAAAAAAA8L8"], [8, 1, "add_file_header", {"path": "D:\\myfwd\\客户端\\main.lua"}, "AQAAAAAAAAAAAAAAkgAAAAAAAAAAAAAA", "EQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADcCgAAAAAAANwKAAAAAAAAAAAAAAAA8L8"], [9, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-07-28 01:17:02"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDctMjggMDA6NTA6Mjc", "EQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABuCwAAAAAAAG4LAAAAAAAAAAAAAAAA8L8"]]}, {"contents": "Searching 314 files for \"商城\" (case sensitive)\n\nD:\\myfwd\\客户端\\Script\\全局\\主控.lua:\n  379  \tself.辅助技能 = {}\n  380  \tself.房屋数据 = {}\n  381: \tself.商城数据 = {}\n  382  \tself.如意符 = false\n  383  \tself.文本回调 = false\n  ...\n  584  \t\t文本栏 = require(\"script/小九UI/文本栏\").创建(self),\n  585  \t\t队标栏 = require(\"script/小九UI/队伍/队标栏\").创建(self),\n  586: \t\t商城类 = require(\"Script/小九UI/商业/商城类\").创建(self),\n  587: \t\t商城宝宝查看 = require(\"script/小九UI/商业/商城宝宝查看\").创建(self),\n  588  \t\t炼药栏 = require(\"Script/小九UI/炼药栏\").创建(self),\n  589  \t\t给予NPC = require(\"script/小九UI/给予NPC\").创建(self),\n\nD:\\myfwd\\客户端\\Script\\初系统\\游戏更新说明.lua:\n   91  尊敬的玩家，你好！#2\n   92  欢迎来到梦幻]西游0NLINE (经典仿官复古版)本服务器采用双模式运营，为保证相对公平，不售出任何道具(消耗品除外)\n   93: #L/无商城，无赞助，无充值，无VIP,无定制，无抽奖\n   94  为保证游戏正常运营维护，采用收取点卡方式获取少量收益高质量复古仿官养老服，本服所有道具皆有价值，藏宝阁系统支持买卖装备\n   95  #R/[点卡模式]\n\nD:\\myfwd\\客户端\\Script\\功能界面\\底图框.lua:\n   33  \tself.临时背包 = 按钮.创建(资源:载入('wzife.wdf',\"网易WDF动画\",0x237d38da),0,0,4,true)\n   34  \tself.背包闪烁 = 资源:载入('wzife.wdf',\"网易WDF动画\",0x237d38da)\n   35: \tself.商城按钮=按钮.创建(资源:载入('wzife.wdf',\"网易WDF动画\",0x61D49AEC),0,0,1,true)\n   36  \ttp = 根\n   37  \tself.临时背包闪烁=false\n   ..\n  124  \n  125  \n  126: \t\tself.商城按钮:更新(x,y)\n  127: \t\tself.商城按钮:显示(全局游戏宽度-50,全局游戏高度-120)\n  128  \t\tif 引擎.场景.队伍[1].体验状态 then\n  129  \t\t\tself.UI_体验状态:更新(x,y)\n  ...\n  154  \t\t\ttp.鼠标.置鼠标(\"平时攻击\")\n  155  \t\t\t\n  156: \t\telseif self.商城按钮:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.D)) and not tp.消息栏焦点 then\n  157: \t\t\ttp.窗口.商城类:打开()\n  158  \t\t\t\n  159  \t\telseif self.UI_成就:事件判断() or self:检查按钮扩展点击(\"UI_成就\", x, y) then\n\nD:\\myfwd\\客户端\\Script\\多重对话类\\对话栏.lua:\n  513  \t\treturn 0\n  514  \telseif 名称 == \"神兽使者 \" then\n  515: \t\ttp.窗口.商城类.发送信息.物法=事件 --物理型（单点爆伤）\n  516: \t\t发送数据(30,tp.窗口.商城类.发送信息)\n  517  \t\tself.可视 = false\n  518  \t\tself.文本栏焦点 = false\n\nD:\\myfwd\\客户端\\Script\\小九UI\\庭院管理.lua:\n   96  \tself.zts:显示(self.x+46,self.y+237,\"植物数量:\"..self.植物总数)\n   97  \tself.zts:显示(self.x+46,self.y+257,\"动物数量:\"..self.动物总数)\n   98: \tself.zts:显示(self.x+46,self.y+277,\"家具请前往商城其他类使用积分购买\")\n   99  \tself.zts:置颜色(白色)\n  100  \tself.资源组[5]:显示(self.x+42,self.y+318)\n\nD:\\myfwd\\客户端\\Script\\小九UI\\法宝.lua:\n  360  \t\tend\n  361  \t\tif self.sq.小动画:是否选中(x,y) then\n  362: \t\t\ttp.提示:商城提示(self.x+70,self.y-50,门派神器名称[tp.队伍[1].门派],self.sq.说明,self.sq.大动画)\n  363  \t\t\tif 引擎.鼠标弹起(右键) then\n  364  \t\t\t\t发送数据(3734,{序列=self.神器格子,神器=true})\n\nD:\\myfwd\\客户端\\Script\\小九UI\\系统设置.lua:\n  419  \t\t\tself:打开()\n  420  \t\telseif self.资源组[201]:事件判断() then\n  421: \t\t\ttp.窗口.商城类:打开()\n  422  \t\telseif self.资源组[202]:事件判断() then\n  423  \t\t\t发送数据(103)\n\nD:\\myfwd\\客户端\\Script\\小九UI\\铃铛抽奖.lua:\n   84  \t\t\t\t\tself.焦点=true\n   85  \t\t\t\t\tif not self.动画开关 then\n   86: \t\t\t\t\t\ttp.提示:商城提示(wx,wy,n.名称,n.说明,n.大动画,n.备注)\n   87  \t\t\t\t\tend\n   88  \t\t\t\t\tif 引擎.鼠标弹起(0) and not self.动画开关 then\n\nD:\\myfwd\\客户端\\Script\\小九UI\\召唤兽\\召唤兽属性栏.lua:\n  302  \t\t\tif self.饰品图标:是否选中(x,y) then\n  303  \t\t\t\ttp.禁止关闭 = true\n  304: \t\t\t\ttp.提示:商城提示(self.x+200,self.y+145,self.饰品.名称,self.饰品.说明,self.饰品.大动画,self.饰品.备注)\n  305  \t\t\t\tif mouseb(1) then\n  306  \t\t\t\t\t发送数据(5019,{bb.认证码})\n\nD:\\myfwd\\客户端\\Script\\小九UI\\商业\\商城宝宝查看.lua:\n    3  -- @Last Modified by:   交流QQ群：834248191\n    4  -- @Last Modified time: 2025-07-20 21:54:31\n    5: local 商城宝宝查看 = class()\n    6  local floor = math.floor\n    7  local format = string.format\n    .\n   11  local 显示列表, 当前宝宝, lb, tp, 技能页码, 技能格子, 宝宝类型\n   12  local 资质列表 = {\"攻击资质\",\"防御资质\",\"体力资质\",\"法力资质\",\"速度资质\",\"躲闪资质\",\"成长\"}\n   13: function 商城宝宝查看:初始化(根)\n   14  \ttp = 根\n   15  \tself.ID = 112\n   ..\n   18  \tself.xx = 0\n   19  \tself.yy = 0\n   20: \tself.注释 = \"商城宝宝查看\"\n   21  \tself.可视 = false\n   22  \tself.鼠标 = false\n   ..\n   27  \tself.最大每页技能数 = 12 -- 每页显示的最大技能数\n   28  end\n   29: function 商城宝宝查看:打开(宝宝数据,物法,神兽)\n   30  \t-- 如果窗口已打开且传入了新宝宝，则关闭窗口\n   31  \tif 宝宝数据~=nil and self.可视 then self.可视=false end\n   ..\n  107  \tend\n  108  end\n  109: function 商城宝宝查看:显示(dt,x,y)\n  110  \tself.焦点 = false\n  111  \n  ...\n  218  \tend\n  219  end\n  220: function 商城宝宝查看:检查点(x,y)\n  221  \tif self.资源组[1] ~= nil and self.资源组[1]:是否选中(x,y)  then\n  222  \t\treturn true\n  223  \tend\n  224  end\n  225: function 商城宝宝查看:初始移动(x,y)\n  226  \ttp.运行时间 = tp.运行时间 + 1\n  227  \tif not tp.消息栏焦点 then\n  ...\n  236  \tend\n  237  end\n  238: function 商城宝宝查看:开始移动(x,y)\n  239  \tif self.鼠标 then\n  240  \t\tself.x = x - self.xx\n  ...\n  242  \tend\n  243  end\n  244: return 商城宝宝查看\n  245  \n\nD:\\myfwd\\客户端\\Script\\小九UI\\商业\\商城类.lua:\n    3  -- @Last Modified by:   交流QQ群：834248191\n    4  -- @Last Modified time: 2025-07-20 21:53:33\n    5: local 商城类 = class(窗口逻辑)\n    6  local 鼠标弹起 = 引擎.鼠标弹起\n    7  local 鼠标按住 = 引擎.鼠标按住\n    .\n   15  local ani = 引擎.取战斗模型\n   16  local tp\n   17: local x类 = {\"组1\",\"组2\",\"组3\",\"组4\",\"组5\",\"组6\",\"组7\",\"组8\",\"组9\",\"组10\",\"宝宝\",\"神兽\",\"仙玉商城\"}\n   18: function 商城类:初始化(根)\n   19  \ttp=根\n   20  \tself.ID = 74\n   ..\n   23  \tself.xx = 0\n   24  \tself.yy = 0\n   25: \tself.注释 = \"商城类\"\n   26  \tself.可视 = false\n   27  \tself.鼠标 = false\n   ..\n   50  \t文字=根.字体表.一般字体\n   51  end\n   52: function 商城类:加载数据(数据)\n   53  \tself.数据 = 数据\n   54  end\n   55: function 商城类:打开()\n   56  \tif self.选中编号 ~=0 then\n   57  \t\treturn false\n   ..\n   82  \tself.银两图标=资源:载入('pic/UI12.png',\"图片\")\n   83  \tself.仙玉图标=资源:载入('pic/UI11.png',\"图片\")\n   84: \tself.按钮坐标={银子商城=53,仙玉商城=53+35*1,神兽商城=53+35*2,宝宝商城=53+35*3}\n   85  \tself.资源组 = {\n   86      [0] = 自适应.创建(1,1,600-20,18,1,3,nil,18),\n   ..\n   92  \t[7] = 自适应.创建(3,1,63,19,1,3),\n   93  \t[8] =  自适应.创建(6,0,150,58,3,9),\n   94: \t[11] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,\"银子商城\"),\n   95: \t[12] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,\"仙玉商城\"),\n   96: \t[13] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,\"神兽商城\"),\n   97: \t[14] =  按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,\"宝宝商城\"),\n   98  \t[17] = 按钮.创建(自适应.创建(13,4,80,20,1,3),0,0,4,true,true,\" 上一页\"),\n   99  \t[18] = 按钮.创建(自适应.创建(13,4,80,20,1,3),0,0,4,true,true,\" 下一页\"),\n  ...\n  117  \t\tself.选中编号2 = 0\n  118  \t\tself.选中编号 = 0\n  119: \t\tself.选中总类=\"银子商城\"\n  120  \t\tself.本类开关 = true\n  121  \t\tself.组号 = 18\n  122: \t\tself.物品数据 = {组1={},组2={},组3={},组4={},组5={},组6={},组7={},组8={},组9={},组10={},宝宝={},神兽={},仙玉商城={}}\n  123  \t\tself.商品编号 = 0\n  124  \t\tfor n = 1,#x类 do\n  ...\n  153  \tend\n  154  end\n  155: function 商城类:显示(dt,x,y)\n  156  \tself.焦点=false\n  157      self.资源组[1]:显示(self.x,self.y)\n  ...\n  171  \t\tself.资源组[i]:更新(x,y)\n  172  \tend\n  173: \tself.资源组[11]:显示(self.x+10,self.y+self.按钮坐标.银子商城)\n  174: \tself.资源组[13]:显示(self.x+10,self.y+self.按钮坐标.神兽商城)\n  175: \tself.资源组[14]:显示(self.x+10,self.y+self.按钮坐标.宝宝商城)\n  176: \tself.资源组[12]:显示(self.x+10,self.y+self.按钮坐标.仙玉商城)\n  177  \tself.控件类:更新(dt,x,y)\n  178  \tself.控件类:显示(x,y)\n  179  \t\tif self.资源组[11]:事件判断() then\n  180: \t\t\tself.选中总类=\"银子商城\"\n  181  \t\t\tself.分类=\"组1\"\n  182  \t\t\tself:初始系统(dt,x,y,2)\n  183  \t\t\tself.加入=0\n  184  \t\telseif self.资源组[12]:事件判断() then\n  185: \t\t\tself.选中总类=\"仙玉商城\"\n  186: \t\t\tself.分类=\"仙玉商城\"\n  187  \t\t\tself:初始系统(dt,x,y,2)\n  188  \t\t\tself.加入=0\n  ...\n  244  \t\tend\n  245  \tend\n  246: \tif self.选中总类==\"银子商城\" then\n  247  \t\tself.资源组[20]:更新(x,y,self.分类~=\"组1\")\n  248  \t\tself.资源组[21]:更新(x,y,self.分类~=\"组2\")\n  ...\n  329  \t文字:置颜色(白色):显示(self.x+100+320+10,self.y+210+60,\"拥有\")\n  330  \n  331: \t\t\tif self.分类 == \"仙玉商城\" or self.分类 == \"神兽\"  then\n  332  \t\t\t\tself.仙玉图标:显示(self.x+100+320+40-3,self.y+210+60-3)\n  333  \t文字:置颜色(取金钱颜色(tp.仙玉))\n  ...\n  349  \t\t\t\tend\n  350  end\n  351: function 商城类:初始系统(dt,x,y,l)\n  352  \tif l~=nil and l==2 then\n  353  \t\tself.商品编号=0\n  ...\n  371  \tself.翻页数据 = math.floor(self.物品数据[self.分类].数量的/10)\n  372  end\n  373: function 商城类:召唤兽类(dt,x,y)\n  374  \tself.输入框:置可视(true,true)\n  375  \ttp.字体表.华康字体:置颜色(红色):显示(self.x+365,self.y+370,\"当前:\"..(self.加入+1)..\"/\"..(self.翻页数据+1))\n  ...\n  429  \t\t\t\tself.查看状态=self.查看状态+1\n  430  \t\t\t\tif self.分类==\"神兽\" and self.查看状态==1 then\n  431: \t\t\t\t\ttp.窗口.商城宝宝查看:打开(self.数据[self.分类][n+self.加入*10],\"物理\",true)\n  432  \t\t\t\telseif self.分类==\"神兽\" and self.查看状态==2 then\n  433: \t\t\t\t\ttp.窗口.商城宝宝查看:打开(self.数据[self.分类][n+self.加入*10],\"法系\",true)\n  434  \t\t\t\telse\n  435: \t\t\t\t\ttp.窗口.商城宝宝查看:打开(self.数据[self.分类][n+self.加入*10],\"宝宝\")\n  436  \t\t\t\tend\n  437  \t\t\t\tif self.查看状态>=2 then\n  ...\n  483  \n  484  \n  485: function 商城类:置形象()\n  486      self.动画 = {}\n  487      self.资源组[30] = nil  -- 重置装饰动画\n  ...\n  501  end\n  502  \n  503: function 商城类:物品类(dt,x,y)\n  504  \tself.输入框:置可视(true,true)\n  505  \ttp.字体表.华康字体:置颜色(红色):显示(self.x+365,self.y+370,\"当前:\"..(self.加入+1)..\"/\"..(self.翻页数据+1))\n  ...\n  549  \t\t        self.介绍文本:显示(self.x + 445,self.y + 58)\n  550  \t\t    end\n  551: \t\t\tif self.分类 == \"仙玉商城\" then\n  552  \t\t\t\tself.仙玉图标:显示(self.x+偏移x + xx * 150 + 75-8-4,self.y+偏移y + yy * 60 +64-4)\n  553  \t\t\telse\n  ...\n  586  \tend\n  587  end\n  588: return 商城类\n  589  \n\nD:\\myfwd\\客户端\\Script\\小九UI\\藏宝阁\\藏宝阁类.lua:\n   48  \t\t[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),\n   49  \t\t[3] = 自适应.创建(4,1,665,355,3,nil),\n   50: \t\t[20] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"装备商城\"),\n   51: \t\t[21] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"灵饰商城\"),\n   52: \t\t[22] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"宝宝商城\"),\n   53: \t\t[4] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"银两商城\"),\n   54: \t\t[5] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"其他商城\"),\n   55  \t\t[6] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"价格排序\"),\n   56  \t\t[7] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"时间排序\"),\n   ..\n   63  \t\t[17] = 按钮.创建(tp.资源:载入(tp.全局资源包,\"网易WDF动画\",0xCB50AB1D),0,0,3,true,true),\n   64  \t\t[18] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"取回物品\"),\n   65: \t\t[19] = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,\"人物商城\"),\n   66  \t\t}\n   67  \t\tself.线 = tp.资源:载入(\"wzife.wdf\",\"网易WDF动画\",999600305)\n\nD:\\myfwd\\客户端\\Script\\小九UI\\角色\\奇经八脉.lua:\n  523  \tif self.神器.小动画:是否选中(x,y) then\n  524  \t\ttp.物品格子焦点_:显示(self.x+202+6,self.y+358+5)\n  525: \t\ttp.提示:商城提示(x,y,self.神器.名称,self.神器.说明,self.神器.大动画)\n  526  \telseif self.法宝.小动画:是否选中(x,y) then\n  527  \t\ttp.物品格子焦点_:显示(self.x+27+6,self.y+358+5)\n  528: \t\ttp.提示:商城提示(x,y,self.法宝.名称,self.法宝.说明,self.法宝.大动画)\n  529  \tend\n  530  \tif tp.队伍[1].奇经八脉.当前流派==tp.队伍[1].奇经八脉[1] then\n\nD:\\myfwd\\客户端\\Script\\小九UI\\角色\\成长礼包.lua:\n  136  \t\t\tend\n  137  \t\t\tif v.名称.小动画:是否选中(x,y) then\n  138: \t\t\t\ttp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)\n  139  \t\t\tend\n  140  \t\tend\n  ...\n  147  \t\t\tend\n  148  \t\t\tif v.名称.小动画:是否选中(x,y) then\n  149: \t\t\t\ttp.提示:商城提示(self.x-150+wx,self.y-10,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)\n  150  \t\t\tend\n  151  \t\tend\n  ...\n  158  \t\t\tend\n  159  \t\t\tif v.名称.小动画:是否选中(x,y) then\n  160: \t\t\t\ttp.提示:商城提示(self.x-150+wx,self.y+90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)\n  161  \t\t\tend\n  162  \t\tend\n  ...\n  169  \t\t\tend\n  170  \t\t\tif v.名称.小动画:是否选中(x,y) then\n  171: \t\t\t\ttp.提示:商城提示(self.x-150+wx,self.y+170,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)\n  172  \t\t\tend\n  173  \t\tend\n  ...\n  180  \t\t\tend\n  181  \t\t\tif v.名称.小动画:是否选中(x,y) then\n  182: \t\t\t\ttp.提示:商城提示(self.x-150+wx,self.y+250,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)\n  183  \t\t\tend\n  184  \t\tend\n\nD:\\myfwd\\客户端\\Script\\显示类\\道具详情.lua:\n 1748  \tself.寄存内容.开启提示 = true\n 1749  end\n 1750: function 系统类_提示框:商城提示(x,y,名称,介绍,动画,备注,神兽)\n 1751  \tlocal x1 = x + 30\n 1752  \tlocal y1 = y\n\nD:\\myfwd\\客户端\\Script\\更新类\\神秘宝箱.lua:\n  148  \t\tend\n  149  \t\tif self.商品[n].小动画:是否选中(x,y) then\n  150: \t\t\ttp.提示:商城提示(self.x + xx * 54-14 ,self.y + yy * 51 + 15,self.商品[n].名称,self.商品[n].说明,self.商品[n].大动画,self.商品[n].备注)\n  151  \t\tend\n  152  \t\txx=xx+1\n\nD:\\myfwd\\客户端\\Script\\更新类\\转盘.lua:\n  131  \t\tself.商品[n].小动画:显示(self.x+物品位置[n].x,self.y+物品位置[n].y)\n  132  \t\tif self.商品[n].小动画:是否选中(x,y) then\n  133: \t\t\ttp.提示:商城提示(self.x + 物品位置[n].x ,self.y +物品位置[n].y,self.商品[n].名称,self.商品[n].说明,self.商品[n].大动画,self.商品[n].备注)\n  134  \t\tend\n  135  \tend\n\nD:\\myfwd\\客户端\\Script\\更新类\\铃铛抽奖.lua:\n   79  \t\t\t\t\tself.焦点=true\n   80  \t\t\t\t\tif not self.动画开关 then\n   81: \t\t\t\t\t\ttp.提示:商城提示(wx,wy,n.名称,n.说明,n.大动画,n.备注)\n   82  \t\t\t\t\tend\n   83  \t\t\t\t\tif 引擎.鼠标弹起(0) and not self.动画开关 then\n\nD:\\myfwd\\客户端\\Script\\神器类\\合成灵犀玉.lua:\n  165  \t\t\tself.物品.小动画:显示(self.x+物品坐标[n][1],self.y+物品坐标[n][2])\n  166  \t\t\tif self.物品.小动画:是否选中(x,y) then\n  167: \t\t\t\ttp.提示:商城提示(self.x + 物品坐标[n][1] ,self.y +物品坐标[n][2],\"灵犀之屑\",self.物品.说明,self.物品.大动画)\n  168  \t\t\tend\n  169  \t\telse\n\nD:\\myfwd\\客户端\\Script\\网络\\数据交换.lua:\n 1702  \t\tend\n 1703  \telseif 序号 == 3700 then\n 1704: \t\t引擎.场景.窗口.商城类:加载数据(内容)\n 1705: \telseif 序号 == 3705 then --商城数据\n 1706: \t\ttp.商城数据 = 内容\n 1707  \telseif 序号 == 3700.1 then\n 1708  \t\ttp.窗口.加锁:打开()\n\n83 matches across 20 files\n", "settings": {"buffer_size": 11897, "line_ending": "Windows", "name": "Find Results", "scratch": true}, "undo_stack": []}, {"file": "Script/小九UI/商业/商城类.lua", "settings": {"buffer_size": 17301, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[4, 1, "left_delete", null, "AQAAAM4JAAAAAAAAzgkAAAAAAAABAAAAIA", "AQAAAAAAAAABAAAAzwkAAAAAAADPCQAAAAAAAAAAAAAAAPC/"], [7, 1, "left_delete", null, "AQAAAI8JAAAAAAAAjwkAAAAAAAABAAAAIA", "AQAAAAAAAAABAAAAkAkAAAAAAACQCQAAAAAAAAAAAAAAAPC/"], [10, 1, "left_delete", null, "AQAAAFEJAAAAAAAAUQkAAAAAAAABAAAAIA", "AQAAAAAAAAABAAAAUgkAAAAAAABSCQAAAAAAAAAAAAAAAPC/"], [13, 1, "left_delete", null, "AQAAAMsJAAAAAAAAywkAAAAAAAABAAAAIA", "AQAAAAAAAAABAAAAzAkAAAAAAADMCQAAAAAAAAAAAAAAAPC/"], [17, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-08-01 12:31:58"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDctMjAgMjE6NTM6MzM", "AQAAAAAAAAABAAAA0QkAAAAAAADRCQAAAAAAAAAAAAAAAPC/"]]}, {"file": "Script/全局/主控.lua", "settings": {"buffer_size": 118780, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": []}], "build_system": "Packages/Lua/ggegame.sublime-build", "build_system_choices": [[[["Packages/Lua/ggegame.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "SetGGE"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "AboutGGE"]], ["Packages/Lua/ggegame.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "in", "selected_items": [["in", "Package Control: Install Package"], ["Terminus: Open Terminal", "Terminus: Toggle Panel"], ["Package Control: Install Package", "Package Control: Install Package"], ["Install Package Control", "Package Control: Install Package"], ["Package Control", "Package Control: Install Package"], ["preferences control", "Preferences: Package Control Settings – <PERSON><PERSON>ult"], ["pac", "Package Control: Install Package"], ["", "AutoFileName: <PERSON><PERSON><PERSON>s"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 510.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/客户端", "/D/myfwd/客户端/<PERSON><PERSON>t", "/D/myfwd/客户端/<PERSON><PERSON>t/全局", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/好友", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类", "/D/myfwd/客户端/Script/数据中心"], "file_history": ["/D/myfwd/客户端/<PERSON>ript/神器类/神器查看.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/法宝.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/好友/好友消息.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/好友/好友查看.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/按钮.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/自动抓鬼.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/坐骑/坐骑技能栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/坐骑/坐骑属性栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/客户端/Script/数据中心/梦战造型.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗自动栏.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/光武拓印.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/角色/奇经八脉.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/角色/成就系统.lua", "/D/GGE神来2修复源码/客户端源码/客户端源码/ggethread.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON>ript/数据中心/普通模型库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/加载类.lua", "/D/myfwd/客户端/Script/数据中心/特效库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/动画类.lua", "/D/myfwd/客户端/script/资源类/WAS.lua", "/D/myfwd/客户端/<PERSON>ript/资源类/动画类_X9.lua", "/D/myfwd/客户端/Script/全局/人物.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/染色.lua", "/D/myfwd/客户端/<PERSON>ript/资源类/SP.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/焕彩染色.lua", "/D/myfwd/客户端/<PERSON>ript/数据中心/战斗模型库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/底图框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务追踪栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/时辰.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/梦幻指引.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/角色/成就提示.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/图鉴.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘 - 副本.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化缓冲.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/优化无边框启动器_v2.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/助战系统.lua", "/D/myfwd/客户端/<PERSON>ript/初系统/游戏更新说明.lua", "/D/myfwd/客户端/script/战斗类/战斗自动栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/增强资源管理器使用示例.lua", "/D/myfwd/客户端/Script/数据中心/物品库.lua", "/D/myfwd/客户端/script/全局/主控.lua", "/D/myfwd/客户端/Script/数据中心/场景.lua", "/D/myfwd/客户端/Script/全局/假人.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/内置管理工具.lua", "/D/myfwd/客户端/Script/数据中心/场景NPC.lua", "/D/myfwd/客户端/Script/数据中心/传送表.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/小九UI/角色/人物状态栏.lua", "/C/Users/<USER>/Desktop/7.21/服务端/Script/任务_小九/神器任务/神归昆仑镜.lua", "/D/myfwd/客户端/Script/数据中心/头像库.lua", "/D/myfwd/客户端/script/小九UI/商业/商城宝宝查看.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/商业/商城类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽资质栏.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽查看栏.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/客户端/Script/数据中心/明雷库.lua", "/D/myfwd/客户端/script/全局/变量1.lua", "/D/myfwd/客户端/<PERSON>ript/资源类/WDF.lua", "/D/myfwd/GGE/Extend/文件类.lua", "/D/myfwd/GGE/Core/Game/ggecommon.lua", "/D/myfwd/客户端/script/资源类/加载类.lua", "/D/myfwd/客户端/<PERSON>ript/数据中心/技能库_新.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/属性控制/宝宝.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/多重对话类/对话栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/创建.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/好友/好友列表.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/五子棋.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/分区.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量2.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/D/myfwd/客户端/script/数据中心/战斗模型库.lua", "/D/myfwd/客户端/<PERSON>rip<PERSON>/小九UI/图鉴_百科.lua", "/D/myfwd/客户端/script/数据中心/技能库.lua", "/D/myfwd/客户端/script/显示类/技能_格子.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/显示类/鼠标.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8788.20098.rartemp/梦战造型.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8788.20808.rartemp/普通模型库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa24908.4435.rartemp/main.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/服务端/<PERSON>ript/地图处理类/地图坐标类.lua", "/D/myfwd/客户端/Script/数据中心/音效库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/召唤兽.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/事件.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/召唤兽/召唤兽属性栏.lua", "/D/myfwd/客户端/script/小九UI/角色/成就系统.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统/客户端热更新系统.lua", "/D/myfwd/GGE/Core/Game/gge引擎.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/场景类/传送点.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主显.lua", "/D/myfwd/客户端/script/全局/人物.lua", "/D/myfwd/客户端/script/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/公告弹窗.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/图片显示窗口.lua", "/D/myfwd/客户端/script/功能界面/小地图.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/技能学习.lua", "/D/myfwd/GGE/Core/Server/ggemain.lua", "/D/myfwd/GGE/Core/Server/ggecommon.lua", "/C/Users/<USER>/Desktop/资源类/动画类.lua", "/D/myfwd/客户端/<PERSON>ript/全局/自己_专用.LUA", "/C/Users/<USER>/Desktop/资源类/gge精灵类.lua", "/C/Users/<USER>/Desktop/资源类/动画类_X9.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa2664.35811.rartemp/战斗动画类.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/道具行囊.lua", "/D/myfwd/客户端/<PERSON>ript/战斗类/战斗单位类 - 副本.lua", "/D/myfwd/客户端/script/小九UI/召唤兽/召唤兽属性栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/小地图.lua", "/D/修复备份/7.3/服务端/Script/战斗处理类/ScriptInit.lua", "/D/my2/客户端/script/战斗类/战斗单位类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/系统设置.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/自适应.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量1.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/快捷技能栏.lua", "/D/myfwd/客户端/script/战斗类/战斗单位类.lua", "/D/myfwd/客户端/script/全局/主显.lua", "/C/Users/<USER>/Documents/Tencent Files/308537402/FileRecv/更新内容.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/小地图.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/路径类.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/跑商商店.lua", "/D/2024.07.04团/服务端/Script/对话处理类/对话调用/1001.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/角色/更多属性.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/第二场景.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/地图类.lua", "/C/Users/<USER>/Desktop/修复备份/6.22最终/客户端/Script/战斗类/战斗单位类.lua"], "find": {"height": 32.0}, "find_in_files": {"height": 127.0, "where_history": ["D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端\\rpk,D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>", "D:\\mymh\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端", "D:\\myfwd\\客户端\\Script", "D:\\myfwd\\客户端", "D:\\myfwd\\Client", "D:\\myfwd\\Server\\Script,<project filters>,D:\\myfwd\\Client\\Script", "D:\\myfwd\\Server\\Script,<project filters>"]}, "find_state": {"case_sensitive": true, "find_history": ["商城", "神器", "好友聊天", "梦幻精灵", "[2]", "[4]", "[5]", "自动抓鬼", "抓鬼", "捉鬼", "\"骑 乘\"", "骑乘", "[11]", "坐骑技能", "坐骑属性", "无赦魔诀", "薪火相传", "理直气壮", "龙魂", "开天辟地", "移动事件", "self.方向", "self.初始方向", "移动事件", "回合结束重置", "鬼将", "移动事件", "鬼将", "攻击2", "鬼将", "挨打", "优", "优先", "择优", "挨打", "龙腾", "进阶蝴蝶", "目标=自动栏[3]", "设置法术参数", "战斗命令", "设置法术参数", "战斗自动栏", "妙手空", "妙手", "妙手空空", "取类型选择失败", "取类型选择", "拓印", "请选择拓印样式", "请选择武器后再操作！", "拓印", "神针撼海", "成就", "玄彩娥", "龙卷", "进阶真陀护法攻击", "玄彩娥攻击_魔棒2", "玄彩娥攻击_魔棒1", "玄彩娥", "染色", "炫彩", "玄彩娥", "任务追踪", "队伍", "队伍闪烁", "任务追踪", "指引", "梦幻罗盘", "梦幻指引", "指引", "底图框", "成就", "成就处理", "成就提示", "玄彩娥", "buding2.wdf", "com", "con", "玄彩娥", "天书", "逃命手机", "方正字体", "宠物名称", "图鉴系统", "图鉴西天", "字体", "天书", "当前星座需要天罡积分", "当前星座", "当前", "翻页提示", "左右", "星盘", "游戏客户端", "梦幻西游", "标题", "1050", "助战", "加载完成", "pr", "击退", "开始击退", "白色渲染等待时间", "启动器配置", "等待时间", "HCBT_CREATEWND", "当前版本", "0x8313b73c", "古树", "播放特效", "8e92ae82", "斜", "斜月", "自爆", "明雷", "7103", "map", "锁定信息", "集合分类(2", "集合分类", "锁定信息", "副本积分", "管理工具", "混天绫", "天师驱魔", "文老伯", "天师驱魔符", "白衣人令牌"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["请选择需要转换的拓印样式", "请选择拓印样式后在做操作！", "超级神虎（壬寅）", "", "全局游戏宽度-355", "全局游戏宽度-360", "虞悄悄", "--print", "item.wdf", "zts:显示(self.x+243+100,self.y+68,打造说明[self.分类标识][self.功能标识])", "zts:显示(self.x+177+100,self.y+68,\"需要材料：\")", "zts:显示(self.x+177+144,self.y+68,\"需要材料：\")", "zts:显示(self.x+243+144,self.y+68,打造说明[self.分类标识][self.功能标识])", "战斗类", "玩家", "武器", "行为", "玩家", "资源", "common/zuoqiyws.wdf", "特效宝珠", "shape.wdf", "shape.wd5", "shape.wd4", "祥瑞坐骑.wdf", "shape.wdf", "common/shape.wdf", "", "pgs_1", "shape.wdf", "pgs_1", "shape.wdf", "", "y", "资源", "（过去）", "（未来）", "", "diedai.wdf", "", "资源", "\"wzife.wdf\"", "资源", "载入('wzife.wdf',\"网易WDF动画\",0X140BBA9),"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 4495, "regions": {}, "selection": [[4016, 4016]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 7, 20, 22, 12, 6, 1, 39, 43, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 2, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 4027.0, "zoom_level": 1.0}, "stack_index": 3, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "semi_transient": false, "settings": {"buffer_size": 11897, "regions": {"match": {"flags": 112, "regions": [[137, 139], [320, 322], [350, 352], [375, 377], [408, 410], [669, 671], [963, 965], [1103, 1105], [1130, 1132], [1291, 1293], [1378, 1380], [1572, 1574], [1622, 1624], [1890, 1892], [2076, 2078], [2334, 2336], [2519, 2521], [2740, 2742], [3026, 3028], [3252, 3254], [3368, 3370], [3497, 3499], [3652, 3654], [3747, 3749], [3885, 3887], [4006, 4008], [4120, 4122], [4284, 4286], [4491, 4493], [4512, 4514], [4622, 4624], [4730, 4732], [4791, 4793], [4969, 4971], [4977, 4979], [4990, 4992], [5003, 5005], [5233, 5235], [5303, 5305], [5373, 5375], [5443, 5445], [5662, 5664], [5811, 5813], [5912, 5914], [6090, 6092], [6147, 6149], [6204, 6206], [6261, 6263], [6378, 6380], [6519, 6521], [6544, 6546], [6654, 6656], [6852, 6854], [7019, 7021], [7182, 7184], [7431, 7433], [7555, 7557], [7641, 7643], [7773, 7775], [7889, 7891], [8136, 8138], [8286, 8288], [8506, 8508], [8576, 8578], [8646, 8648], [8715, 8717], [8784, 8786], [9148, 9150], [9367, 9369], [9517, 9519], [9733, 9735], [9897, 9899], [10061, 10063], [10225, 10227], [10390, 10392], [10581, 10583], [10759, 10761], [11048, 11050], [11287, 11289], [11540, 11542], [11740, 11742], [11786, 11788], [11803, 11805]], "scope": ""}}, "selection": [[4285, 4285]], "settings": {"detect_indentation": false, "line_numbers": false, "output_tag": 1, "result_base_dir": "", "result_file_regex": "^([^ \t].*):$", "result_line_regex": "^ +([0-9]+):", "scroll_past_end": true, "syntax": "Packages/Default/Find Results.hidden-tmLanguage"}, "translation.x": 0.0, "translation.y": 1794.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "Script/小九UI/商业/商城类.lua", "selected": true, "semi_transient": false, "settings": {"buffer_size": 17301, "regions": {}, "selection": [[2513, 2513]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 21, 34, 12, 205, 207, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 1725.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}, {"buffer": 3, "file": "Script/全局/主控.lua", "semi_transient": false, "settings": {"buffer_size": 118780, "regions": {}, "selection": [[23782, 23782]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 21, 34, 9, 233, 231, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 13030.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 32.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.Terminus": {"height": 150.0}, "output.exec": {"height": 140.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "project": "开发客户端.sublime-project", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["战斗命令", "Client\\Script\\战斗类\\战斗命令类.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 267.0, "status_bar_visible": true, "template_settings": {}}