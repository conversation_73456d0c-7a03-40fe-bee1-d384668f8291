{"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 4251, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[9, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-07-23 00:16:19"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDUtMTkgMjM6NDI6Mzk", "IQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAANgBAAAAAAAA2AEAAAAAAAAAAAAAAADwvw"]]}, {"contents": "Searching 376 files for \"吸附石\"\n\nD:\\myfwd\\服务端\\Script\\数据中心\\物品数据.lua:\n 3744  \t}\n 3745  \tItemData[\"点化石\"]={\n 3746: \twp_1=\"吸附石在成功吸取召唤兽身上的特殊能力后转变而成的打造材料，与召唤兽装备合成后即可成为套装之一。\"\n 3747  \t,wp_2 = 5\n 3748  \t,wp_3 = 5\n ....\n 3788  \t,wp_13 = 0x5B75710B\n 3789  \t}\n 3790: \tItemData[\"吸附石\"]={\n 3791  \twp_1 =\"精铁铸造的装备中分解而出的特殊材料，与召唤兽进行炼妖会有一定几率获取召唤兽身上的特殊能力。\"\n 3792  \t,wp_2 = 3\n\nD:\\myfwd\\服务端\\Script\\角色处理类\\召唤兽处理类.lua:\n 1184  \t        常规提示(id, \"你没有这么多的银子！\")\n 1185  \t    end\n 1186: \telseif 物品名称==\"吸附石\" then\n 1187  \t\t\tif self:是否有装备(bb) then\n 1188  \t\t\t\t常规提示(id,\"请先卸下召唤兽所穿戴的装备\")\n ....\n 1192  \t\t\t\treturn\n 1193  \t\t\telseif self.数据[bb].种类==\"神兽\" then\n 1194: \t\t\t\t常规提示(id,\"神兽不能使用吸附石！\")\n 1195  \t\t\t\treturn\n 1196  \t\t\t-- elseif self.数据[bb].种类==\"野怪\" then\n\nD:\\myfwd\\服务端\\Script\\角色处理类\\装备处理类.lua:\n 1885                  else\n 1886                      for i=1,分解获得 do\n 1887:                         玩家数据[id].道具:给予道具(id,\"吸附石\")\n 1888                      end\n 1889                  end\n\nD:\\myfwd\\服务端\\Script\\角色处理类\\道具处理类.lua:\n  797  \t\tend\n  798  \n  799: \t\t-- 先消耗吸附石\n  800: \t\tif 玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,\"吸附石\",1) then\n  801  \t\t\t-- 判断吸附是否成功\n  802  \t\t\tif 取随机数() <= 吸附概率 then\n  ...\n  806  \t\t\t\t--常规提示(id,\"#Y/吸附成功！获得了点化石\")\n  807  \t\t\telse\n  808: \t\t\t\t-- 失败：只消耗了吸附石，兽决保留\n  809  \t\t\t\t常规提示(id,\"#R/吸附失败！\")\n  810  \t\t\tend\n  ...\n 2164  \t\t\t道具使用=true\n 2165  \t\t\t发送数据(玩家数据[id].连接id,1501,{名称=\"修业道童\",模型=\"道童\",对话=format(\"五行相生相克，循环不断，生生不息！恭喜你修为增加了！\")})\n 2166: \t\telseif 名称==\"吸附石\" then\n 2167  \t\t\t发送数据(玩家数据[id].连接id,245)\n 2168  \t\t\treturn\n ....\n 4254  \t\t\tend\n 4255  \t\t\tself.数据[道具id].分类=3\n 4256: \t\telseif 名称==\"吸附石\" then\n 4257        \t\tself.数据[道具id].五行=取五行()\t\n 4258  \t\telseif 名称==\"怪物卡片\" then\n\n10 matches across 4 files\n\n\nSearching 376 files for \"bb套\"\n\nD:\\myfwd\\服务端\\Script\\战斗处理类\\战斗处理.lua:\n 2257              elseif self.参战单位[n].类型 == \"bb\" then\n 2258                  if self.参战单位[n].装备 ~= nil then\n 2259:                     self:检查bb套装(self.参战单位[n])\n 2260                  end\n 2261                  self:添加技能属性(self.参战单位[n], self.参战单位[n].技能)\n ....\n 2490  end\n 2491  \n 2492: function 战斗处理类:检查bb套装(bb)\n 2493      local 临时套装 = {}\n 2494      local 套装统计 = {}  -- 统计每个套装名称的数量\n ....\n 6225          if self.参战单位[n].类型 ~= \"角色\" then\n 6226              if self.参战单位[n].装备 ~= nil then\n 6227:                 self:检查bb套装(self.参战单位[n])\n 6228              end\n 6229              self:添加技能属性(self.参战单位[n], self.参战单位[n].技能)\n\n3 matches in 1 file\n\n\nSearching 376 files for \"点化\"\n\nD:\\myfwd\\服务端\\Script\\任务_小九\\副本任务\\乌鸡国.lua:\n  597  \t\t\t成就数据[cyid]:判断进度(cyid, \"副本首通\", \"乌鸡国\")\n  598  \n  599: \t\t\t-- 完成乌鸡国副本后自动触发装备点化任务\n  600  \t\t\tif 玩家数据[cyid].角色:取任务(1600) == 0 then\n  601: \t\t\t\tlocal 点化任务id = cyid .. \"_1600_\" .. os.time() .. \"_\" .. 取随机数(1000, 9999)\n  602: \t\t\t\t任务数据[点化任务id] = {\n  603: \t\t\t\t\tid = 点化任务id,\n  604  \t\t\t\t\t起始 = os.time(),\n  605  \t\t\t\t\t结束 = 9999999999, -- 永久任务，直到完成\n  ...\n  608  \t\t\t\t\t类型 = 1600\n  609  \t\t\t\t}\n  610: \t\t\t\t玩家数据[cyid].角色:添加任务(点化任务id)\n  611: \t\t\t\t常规提示(cyid, \"#Y你在乌鸡国的历练让你感悟到了装备的奥秘，请去找五行大师打听关于装备点化的事情。\")\n  612  \t\t\tend\n  613  \n  ...\n  985  end\n  986  \n  987: -- 装备点化任务说明函数\n  988  function 任务说明1600(玩家id, 任务id)\n  989  \tlocal 说明 = {}\n  990: \t说明 = { \"装备点化\", \"#L请去找五行大师打听关于装备点化的事情。\" }\n  991  \treturn 说明\n  992  end\n\nD:\\myfwd\\服务端\\Script\\假人处理类\\假人玩家.lua:\n  766  \telseif 摆摊数据.总类==2000 then\n  767  \t\t摆摊数据.耐久度=100\n  768: \telseif 名称==\"点化石\" then\n  769  \t\tif 取随机数()<=2 then\n  770  \t\t\t摆摊数据.附带技能=\"剑荡四方\"\n  ...\n  887  \t\t[\"上古锻造图策\"] = true,\n  888  \t\t[\"天眼珠\"] = true,\n  889: \t\t[\"点化石\"] = true,\n  890  \t\t[\"灵饰指南书\"] = true,\n  891  \t\t[\"元灵晶石\"] = true,\n  ...\n  963  \t\t\t[\"上古锻造图策\"] = true,\n  964  \t\t\t[\"天眼珠\"] = true,\n  965: \t\t\t[\"点化石\"] = true,\n  966  \t\t\t[\"灵饰指南书\"] = true,\n  967  \t\t\t[\"元灵晶石\"] = true,\n\nD:\\myfwd\\服务端\\Script\\对话处理类\\NPC对话处理.lua:\n  816  \t\t\treturn true\n  817  \t\tend\n  818: \telseif 名称==\"点化装备套装\" then\n  819: \t\tif 事件~=\"我暂时先不点化了\" then\n  820  \t\t\tlocal 强化石 = {\"青龙石\",\"朱雀石\",\"玄武石\",\"白虎石\"}\n  821: \t\t\tlocal 套装类型 = 玩家数据[数字id].点化套装数据.套装\n  822: \t\t\tlocal 宝珠id = 玩家数据[数字id].点化套装数据.宝珠数据\n  823  \t\t\tlocal id = 数字id\n  824: \t\t\tlocal 装备编号 = 玩家数据[数字id].角色.道具[玩家数据[数字id].点化套装数据.装备]\n  825  \t\t\tlocal 装备等级 = 玩家数据[数字id].道具.数据[装备编号].级别限制\n  826  \t\t\tlocal 消耗石头 = 玩家数据[数字id].道具.数据[装备编号].分类\n  ...\n  836  \t\t\t\tend\n  837  \t\t\tend\n  838: \t\t\tif 玩家数据[数字id].点化套装数据.装备 == nil or 玩家数据[数字id].点化套装数据.套装==0 or 玩家数据[数字id].点化套装数据.套装==nil then\n  839  \t\t\t\t常规提示(数字id,\"道具数据异常，请重新打开界面进行操作。\")\n  840  \t\t\t\treturn true\n  ...\n  845  \t\t\tend\n  846  \t\t\tif 玩家数据[数字id].角色.当前经验 < 装备等级*3000 then\n  847: \t\t\t\t常规提示(数字id,\"您的经验不足，无法进行点化。\")\n  848  \t\t\t\treturn true\n  849  \t\t\tend\n  850  \t\t\tif 玩家数据[数字id].角色.银子 < 装备等级*5000 then\n  851: \t\t\t\t常规提示(数字id,\"您的银子不足，无法进行点化。\")\n  852  \t\t\t\treturn true\n  853  \t\t\tend\n  854  \t\t\tif 玩家数据[数字id].道具:判定背包道具(数字id,强化石数据[1],强化石数据[2])==false then\n  855: \t\t\t\t常规提示(数字id,\"您的\"..强化石[消耗石头]..\"不足，无法进行点化。\")\n  856  \t\t\t\treturn true\n  857  \t\t\tend\n  ...\n  868  \t\t\t\t玩家数据[数字id].道具.数据[装备编号].祈福值 = nil\n  869  \t\t\t\t玩家数据[数字id].角色.当前经验=玩家数据[数字id].角色.当前经验-装备等级*3000\n  870: \t\t\t\t玩家数据[数字id].角色:扣除银子(装备等级*5000,0,0,\"点化套装\",1)\n  871: \t\t\t\t常规提示(数字id,\"点化成功\")\n  872  \t\t\t\t道具刷新(数字id)\n  873  \t\t\tend\n  874: \t\t\t玩家数据[数字id].点化套装数据 = nil\n  875  \t\tend\n  876  \t\treturn true\n\nD:\\myfwd\\服务端\\Script\\对话处理类\\对话调用\\1001.lua:\n  165  \telseif 编号 == 23 then\n  166  \t\twb[1] = \"当年有志学长生，今日方知道行精。运动乾坤颠倒理，转移日月互为明。\"\n  167: \t\tlocal xx = {\"请帮我点化装备\",\"更改装备五行\",\"更改法宝五行\",\"更改法宝材料五行\",\"我点错了\"}\n  168  \t\treturn{\"五行大师\",\"五行大师\",wb[取随机数(1,#wb)],xx}\n  169  \telseif 编号 == 24 then\n  ...\n  240  \t\treturn{\"衙役\",\"建房史\",wb[取随机数(1,#wb)],xx}\n  241  \telseif 编号 == 60 then\n  242: \t\twb[1] = \"装备需要通过开运增加开运孔数才能进行符石镶嵌,而点化此乃上古秘传道术贫道近期正修炼此法,你有兴趣的话可以来试试。你可考虑清楚,分到虽习得此法,但道行修行尚浅,且需运筹乾坤,难免开运失败。\"\n  243: \t\tlocal xx = {\"我来给装备开运\",\"点化装备星位\",\"我来合成符石\",\"我什么都不想做\"}\n  244  \t\treturn{\"道童\",\"符石道人\",wb[取随机数(1,#wb)],xx}\n  245  \telseif 编号 == 67 then\n  ...\n  591  \t\tend\n  592  \telseif 名称==\"五行大师\" then\n  593: \t\tif 事件==\"请帮我点化装备\" or 事件==\"继续点化\" then\n  594: \t\t\t-- 检查是否有装备点化任务\n  595: \t\t\tlocal 点化任务id = 玩家数据[数字id].角色:取任务(1600)\n  596: \t\t\tif 点化任务id == 0 then\n  597: \t\t\t\t添加最后对话(数字id, \"少侠，装备点化乃是上古秘术，需要特殊的机缘才能传授。你可先去历练一番，待时机成熟再来找我。\")\n  598  \t\t\t\treturn\n  599  \t\t\tend\n  600  \n  601: \t\t\t玩家数据[数字id].给予数据={类型=1,id=0,事件=\"点化装备\"}\n  602  \t\t\t发送数据(id,3530,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=\"五行大师\",类型=\"NPC\",等级=\"无\"})\n  603  \t\telseif 事件==\"更改法宝五行\" then\n  ...\n 1368  \t\tif 事件==\"我来给装备开运\" then\n 1369  \t\t\t发送数据(玩家数据[数字id].连接id,300,{\"装备开运\",{类型=1}})\n 1370: \t\telseif 事件==\"点化装备星位\" then\n 1371  \t\t\t发送数据(玩家数据[数字id].连接id,300,{\"装备开运\",{类型=2}})\n 1372  \t\telseif 事件==\"我来合成符石\" then\n\nD:\\myfwd\\服务端\\Script\\对话处理类\\对话调用\\1092.lua:\n   16  \tlocal xx = {}\n   17  \tif 编号 == 1 then\n   18:   wb[1] = \"我这里可以用#G钓鱼积分#兑换海产：\\n强化石，彩果，金柳露，点化石，附魔宝珠，魔兽要诀，神兜兜\\n你目前有\"..玩家数据[数字id].角色.钓鱼积分..\"积分\"\n   19    local xx = {\"花2000两购买鱼竿\",\"我要兑换奖励\",\"取消\"}\n   20  \t\treturn {\"钓鱼\",\"渔夫\",wb[sj(1,#wb)],xx}\n\nD:\\myfwd\\服务端\\Script\\数据中心\\场景NPC.lua:\n   33  \t\t假人[21] = {名称=\"袁天罡\",模型=\"袁天罡\",X=357,Y=280-245,方向=0,执行事件=\"不执行\",小地图名称颜色=0}\n   34  \t\t假人[22] = {名称=\"李将军\",模型=\"马副将\",称谓=\"官职任务\",X=161,Y=280-190,方向=0,执行事件=\"不执行\",小地图名称颜色=0}\n   35: \t\t假人[23] = {名称=\"五行大师\",模型=\"五行大师\",称谓=\"点化套装\",X=355,Y=280-115,方向=0,执行事件=\"不执行\",小地图名称颜色=0}\n   36  \t\t假人[24] = {名称=\"杜少海\",模型=\"店小二\",称谓=\"初出江湖\",X=407,Y=211,方向=1,执行事件=\"不执行\",小地图名称颜色=0}\n   37  \t\t假人[25] = {名称=\"御林军左统领\",模型=\"护卫\",称谓=\"皇宫飞贼\",X=102,Y=280-227,方向=0,执行事件=\"不执行\",小地图名称颜色=0}\n\nD:\\myfwd\\服务端\\Script\\数据中心\\物品数据.lua:\n  570  \t}\n  571  \tItemData[\"和田白玉\"]={\n  572: \twp_1=\"临江之畔，璞石无光，千年白玉，温润有方。有一定的概率为人物装备点化出#R无级别限制#W特效。\\n#Y【使用限制】≤140级\"\n  573  \t,wp_2=\"银票\"\n  574  \t,wp_3 = 3\n  ...\n 3055  \t}\n 3056  \tItemData[\"空白强化符\"]={\n 3057: \twp_1=\"传说世间中流传着一些神奇的法术，可以点化装备为其附加某种临时属性。而懂得这些法术的人也能将一张空白的强化符进行点化，使其成为有着神奇功效的灵符。\"\n 3058  \t,wp_2 = 101\n 3059  \t,wp_3 = 3\n ....\n 3743  \t,wp_13 = 0x2D3FDF9\n 3744  \t}\n 3745: \tItemData[\"点化石\"]={\n 3746  \twp_1=\"吸附石在成功吸取召唤兽身上的特殊能力后转变而成的打造材料，与召唤兽装备合成后即可成为套装之一。\"\n 3747  \t,wp_2 = 5\n ....\n 3838  \t}\n 3839  \tItemData[\"炼兽丹\"]={\n 3840: \twp_1 =\"神奇的丹器,点化后能够将你潜心修炼所得经验纳入丹內让召唤兽内丹领悟到下一级\"\n 3841  \t,wp_2 = 110\n 3842  \t,wp_3 = 1\n ....\n 7471  \t}\n 7472  \tItemData[\"游龙惊鸿\"]={\n 7473: \twp_1=\"此鞭舞之矫如游龙，翩若惊鸿，宛如活物。据传是因仙家点化，方有此功效。\"\n 7474  \t,wp_2 = 2\n 7475  \t,wp_3 = 3\n ....\n 9847  \t}\n 9848  \tItemData[\"点星芒\"]={\n 9849: \twp_1=\"福禄寿三星以自身星芒点化而成的异石，紫芒莹然，贵不可言。\"\n 9850  \t,wp_2 = 2\n 9851  \t,wp_3 = 10\n ....\n 14205  \t}\n 14206  \tItemData[\"修炼石\"]={\n 14207: \twp_1=\"珍贵稀有的炼妖神品，能令召唤兽回到原始状态，并有一定几率点化出神兽\"\n 14208  \t,wp_2 = 138\n 14209  \t,wp_3 = 1\n\nD:\\myfwd\\服务端\\Script\\系统处理类\\小神网关.lua:\n   61  \t\t\t\tif data[2] == \"灵饰指南书\" then --给予道具(id,名称,数量,参数,\n   62  \t\t\t\t\t玩家数据[k].道具:给予道具(k, data[2], { [1] = data[3] + 0 }, data[4])\n   63: \t\t\t\telseif data[2] == \"点化石\" then\n   64  \t\t\t\t\t玩家数据[k].道具:给予道具(k, data[2], data[4], data[4])\n   65  \t\t\t\telseif data[2] == \"未激活的符石\" then\n   ..\n   80  \t\t\tif data[2] == \"灵饰指南书\" then --给予道具(id,名称,数量,参数,\n   81  \t\t\t\t玩家数据[data[1]].道具:给予道具(data[1], data[2], { [1] = data[3] + 0 }, data[4])\n   82: \t\t\telseif data[2] == \"点化石\" then\n   83  \t\t\t\t玩家数据[data[1]].道具:给予道具(data[1], data[2], data[4], data[4])\n   84  \t\t\telseif data[2] == \"未激活的符石\" then\n\nD:\\myfwd\\服务端\\Script\\系统处理类\\管理工具类.lua:\n  558    \"凤凰\",\"幽灵\",\"吸血鬼\",\"画魂\",\"雾中仙\",\"机关鸟\",\"巴蛇\",\"猫灵人形\",\"修罗傀儡妖\"}}\n  559    local 附加类型=\"\"\n  560:   if 数据.点化效果 ~= nil and 数据.点化效果 ~= \"\" and tonumber(数据.点化效果) == nil then\n  561      for i=1,#套装效果.附加状态 do\n  562:       if 套装效果.附加状态[i] == 数据.点化效果 then\n  563          附加类型 = 2\n  564          break\n  ...\n  567      if 附加类型 == \"\" then\n  568        for i=1,#套装效果.追加法术 do\n  569:         if 套装效果.追加法术[i] == 数据.点化效果 then\n  570            附加类型 = 1\n  571            break\n  ...\n  575      if 附加类型 == \"\" then\n  576        for i=1,#套装效果.变身法术 do\n  577:         if 套装效果.变身法术[i] == 数据.点化效果 then\n  578            附加类型 = 3\n  579            break\n  ...\n  586    end\n  587    local 倍率=取随机数(下限,上限)\n  588:   装备处理类:发送定制装备(玩家id,临时等级*10,武器序列,武器名称,\"定制装备\",nil,true,专用,特效,nil,倍率,特技,双加,附加类型,数据.点化效果)\n  589  elseif 序号 == 7210 then\n  590    local 临时格子 = 玩家数据[玩家id].角色:取道具格子()\n\nD:\\myfwd\\服务端\\Script\\角色处理类\\召唤兽处理类.lua:\n 1230  \t\t\t\t\t玩家数据[id].角色.道具[物品] = nil\n 1231  \t\t\t\t\ttable.remove(self.数据,bb)\n 1232: \t\t\t\t\t玩家数据[id].道具:给予道具(id,\"点化石\",吸附名称)\n 1233  \t\t\t\t\t常规提示(id,\"#Y/你成功的从召唤兽身上吸取到#R/\"..吸附名称..\"#Y/技能#80\")\n 1234  \t\t\t\t\t发送数据(连接id,16,self.数据)\n\nD:\\myfwd\\服务端\\Script\\角色处理类\\装备处理类.lua:\n 1031          local 宝石格子=0\n 1032          local 装备格子=0\n 1033:         if 玩家数据[id].道具.数据[格子1].总类==5 or 玩家数据[id].道具.数据[格子1].总类==\"召唤兽镶嵌\" or 玩家数据[id].道具.数据[格子1].名称==\"点化石\"  or 玩家数据[id].道具.数据[格子1].名称==\"珍珠\"  then\n 1034              宝石格子=格子1\n 1035              装备格子=格子2\n ....\n 1227                  return\n 1228              end\n 1229:         elseif 功能 == \"点化石\" then\n 1230:             if 玩家数据[id].道具.数据[宝石格子].名称~=\"点化石\" then\n 1231:                 常规提示(id,\"只有点化石才可以进行此操作\")\n 1232                  return\n 1233              end\n 1234:             if 玩家数据[id].道具.数据[宝石格子].名称==\"点化石\"  and 玩家数据[id].道具.数据[装备格子].召唤兽装备 then\n 1235                  if 玩家数据[id].道具.数据[宝石格子].附带技能 then\n 1236:                     local 点化类型 = \"附加状态\"\n 1237                      local ads = {\"善恶有报\",\"力劈华山\",\"壁垒击破\",\"惊心一剑\",\"剑荡四方\",\"水攻\",\"烈火\",\"雷击\",\"落岩\",\"奔雷咒\",\"水漫金山\",\"地狱烈火\",\"泰山压顶\",\"夜舞倾城\"}\n 1238                      for i=1,#ads do\n 1239                          if 玩家数据[id].道具.数据[宝石格子].附带技能==ads[i] then\n 1240:                             点化类型=\"追加法术\"\n 1241                              break\n 1242                          end\n 1243                      end\n 1244:                     玩家数据[id].道具.数据[装备格子].套装效果 = {点化类型,玩家数据[id].道具.数据[宝石格子].附带技能}\n 1245                      if 宝石格子==玩家数据[id].角色.道具[内容.序列] then\n 1246                          玩家数据[id].角色.道具[内容.序列]=nil\n ....\n 1250                          玩家数据[id].道具.数据[宝石格子]=nil\n 1251                      end\n 1252:                     常规提示(id,\"#Y/装备点化成功！\")\n 1253                      道具刷新(id)\n 1254                  end\n\nD:\\myfwd\\服务端\\Script\\角色处理类\\道具处理类.lua:\n  258  \t\tself:宝宝进阶(连接id,数字id,数据)\n  259  \telseif 序号==3763 then\n  260: \t\tself:装备点化套装(连接id,数字id,数据)\n  261  \telseif 序号==3799 then\n  262  \t\t队伍处理类:玩家心情处理(连接id,数字id,数据)\n  ...\n  622  \tend\n  623  end\n  624: function 道具处理类:装备点化套装(连接id,id,sjj)\n  625  \tlocal 强化石 = {\"青龙石\",\"朱雀石\",\"玄武石\",\"白虎石\"}\n  626  \tlocal 套装类型 = sjj.套装\n  ...\n  649  \tend\n  650  \tif self.数据[宝珠id].级别限制<装备等级 then\n  651: \t\t常规提示(id,\"宝珠等级小于装备等级，无法进行点化。\")\n  652  \t\treturn\n  653  \tend\n  654  \tif 玩家数据[id].角色.当前经验 < 装备等级*3000 then\n  655: \t\t常规提示(id,\"您的经验不足，无法进行点化。\")\n  656  \t\treturn\n  657  \tend\n  658  \tif 玩家数据[id].角色.银子 < 装备等级*5000 then\n  659: \t\t常规提示(id,\"您的银子不足，无法进行点化。\")\n  660  \t\treturn\n  661  \tend\n  662  \tif 玩家数据[id].道具:判定背包道具(id,强化石数据[1],强化石数据[2])==false then\n  663: \t\t常规提示(id,\"您的\"..强化石[消耗石头]..\"不足，无法进行点化。\")\n  664  \t\treturn\n  665  \tend\n  ...\n  684  \t\t\tend\n  685  \t\t\tself.数据[装备编号].祈福值 = self.数据[装备编号].祈福值 + 2\n  686: \t\t\t常规提示(id,\"点化成功\")\n  687  \t\t\t玩家数据[id].角色.当前经验=qz(玩家数据[id].角色.当前经验-装备等级*3000)\n  688: \t\t\t玩家数据[id].角色:扣除银子(装备等级*5000,0,0,\"点化套装\",1)\n  689  \t\t\t道具刷新(id)\n  690  \t\tend\n  ...\n  709  \t\t\tend\n  710  \t\tend\n  711: \t\ttable.insert(选项,\"我暂时先不点化了\")\n  712: \t\tlocal 对话=\"装备祈福值已满，您可以选择一个自己想要的套装效果进行祈福：(正在点化：#Z\"..套装类型名称..\"#W)\"\n  713: \t\t玩家数据[id].点化套装数据 = sjj\n  714: \t\t发送数据(连接id,1501,{名称=\"点化装备套装\",模型=玩家数据[id].角色.模型,对话=对话,选项=选项})\n  715  \tend\n  716  end\n  ...\n  801  \t\t\t-- 判断吸附是否成功\n  802  \t\t\tif 取随机数() <= 吸附概率 then\n  803: \t\t\t\t-- 成功：消耗兽决并给予点化石\n  804: \t\t\t\t玩家数据[id].道具:给予道具(id,\"点化石\",吸附名称)\n  805  \t\t\t\tself:删除道具(连接id,id,\"道具\",道具id,gz,删除数量)\n  806: \t\t\t\t--常规提示(id,\"#Y/吸附成功！获得了点化石\")\n  807  \t\t\telse\n  808  \t\t\t\t-- 失败：只消耗了吸附石，兽决保留\n  ...\n 4218  \t\t\tself.数据[道具id].y=xy.y\n 4219  \t\t\t降妖伏魔:逐妖蛊虫(id,self.数据[道具id].地图编号,self.数据[道具id].x,self.数据[道具id].y)\n 4220: \t\telseif 名称==\"点化石\" then\n 4221  \t\t\tself.数据[道具id].附带技能=数量 or \"感知\"\n 4222  \t\telseif  名称==\"九转金丹\" then\n ....\n 6131  \t\tself.数据[道具id].五行=取五行()\n 6132  \t\t添加最后对话(id,self.数据[道具id].名称..\"五行更改为#Y/\"..self.数据[道具id].五行)\n 6133: \telseif 事件==\"点化装备\" then\n 6134  \t\tlocal 道具id=玩家数据[id].角色.道具[数据.格子[1]]\n 6135  \t\tif self.数据[道具id].总类~=2 or self.数据[道具id].灵饰  then\n 6136: \t\t\t添加最后对话(id,\"我这里目前只能点化人物装备，其它的我可没那么大的能耐。\")\n 6137  \t\t\treturn\n 6138  \t\telseif self.数据[道具id].总类 == 2 and self.数据[道具id].分类==3 then\n 6139: \t\t\t添加最后对话(id,\"武器不能点化。\")\n 6140  \t\t\treturn\n 6141  \t\tend\n ....\n 6145  \t\tend\n 6146  \t\tif 玩家数据[id].角色.银子<银子 then\n 6147: \t\t\t添加最后对话(id,format(\"本次点化需要消耗#Y%s#W两银子，你似乎手头有点紧哟？\",银子))\n 6148  \t\t\treturn\n 6149  \t\tend\n 6150: \t\t玩家数据[id].角色:扣除银子(银子,0,0,\"点化装备\",1)\n 6151  \t\tlocal 套装类型={\"附加状态\",\"追加法术\"}\n 6152  \t\t套装类型=套装类型[取随机数(1,#套装类型)]\n ....\n 6169  \t\t\tself.数据[道具id].套装效果={套装类型,套装效果[套装类型][取随机数(1,#套装效果[套装类型])]}\n 6170  \t\tend\n 6171: \t\t-- 装备点化成功后，取消任务1600\n 6172: \t\tlocal 点化任务id = 玩家数据[id].角色:取任务(1600)\n 6173: \t\tif 点化任务id ~= 0 then\n 6174: \t\t\t玩家数据[id].角色:取消任务(点化任务id)\n 6175: \t\t\t任务数据[点化任务id] = nil\n 6176  \t\tend\t\t\n 6177: \t\t添加最后对话(id,format(\"点化装备成功,您本次点化后的套装效果为#Y%s：%s\",self.数据[道具id].套装效果[1],self.数据[道具id].套装效果[2]),{\"继续点化\",\"告辞\"})\n 6178  \telseif 事件==\"更改法宝材料五行\" then\n 6179  \t\tlocal 道具id=玩家数据[id].角色[类型][数据.格子[1]]\n ....\n 6187  \t\t\treturn\n 6188  \t\tend\n 6189: \t\t玩家数据[id].角色:扣除银子(银子,0,0,\"点化装备\",1)\n 6190  \t\tself.数据[道具id].五行=取五行()\n 6191  \telseif 事件==\"更改装备五行\" then\n ....\n 6200  \t\t\treturn\n 6201  \t\tend\n 6202: \t\t玩家数据[id].角色:扣除银子(银子,0,0,\"点化装备\",1)\n 6203  \t\tself.数据[道具id].五行=取五行()\n 6204  \t\t添加最后对话(id,self.数据[道具id].名称..\"五行更改为#Y/\"..self.数据[道具id].五行)\n ....\n 8993  \t\treturn\n 8994  \telseif 装备道具.总类~=2 or 装备道具.灵饰 or 装备道具.召唤兽装备 then\n 8995: \t\t常规提示(id,\"我这里目前只能点化人物装备的星位，其它的我可没那么大的能耐。\")\n 8996  \t\treturn\n 8997  \tend\n ....\n 9143  \t\treturn\n 9144  \telseif 装备道具.总类~=2 or 装备道具.灵饰 or 装备道具.分类==9 or 装备道具.分类==8 or 装备道具.分类==7 then\n 9145: \t\t常规提示(id,\"我这里目前只能点化人物装备的星位，其它的我可没那么大的能耐。\")\n 9146  \t\treturn\n 9147  \telseif not 装备道具.星位组 or 装备道具.星位==nil or 装备道具.星位[6]==nil then\n ....\n 9194  \t\treturn\n 9195  \telseif 装备道具.总类~=2 or 装备道具.灵饰 or 装备道具.分类==9 or 装备道具.分类==8 or 装备道具.分类==7 then\n 9196: \t\t常规提示(id,\"我这里目前只能点化人物装备的星位，其它的我可没那么大的能耐。\")\n 9197  \t\treturn\n 9198  \telseif not 装备道具.星位组 or 装备道具.星位==nil or 装备道具.星位[6]==nil then\n\n100 matches across 12 files\n", "settings": {"buffer_size": 15299, "line_ending": "Windows", "name": "Find Results", "scratch": true}, "undo_stack": []}, {"file": "Script/对话处理类/对话调用/1001.lua", "settings": {"buffer_size": 62478, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": []}, {"file": "Script/对话处理类/NPC对话处理.lua", "settings": {"buffer_size": 30053, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": []}, {"file": "Script/战斗处理类/战斗处理.lua", "settings": {"buffer_size": 463043, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": []}, {"file": "Script/角色处理类/召唤兽处理类.lua", "settings": {"buffer_size": 47235, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": []}, {"file": "Script/角色处理类/道具处理类.lua", "settings": {"buffer_size": 272797, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[8, 1, "insert", {"characters": " "}, "AgAAAPeaAgAAAAAA+JoCAAAAAAAAAAAA+JoCAAAAAAD4mgIAAAAAAJkAAAAJCQnlpZfoo4XmlYjmnpwu6L+95Yqg5rOV5pyvPXsi5LiK5Y+k54G156ymIiwi5rC05pS7Iiwi54Gr5pS7Iiwi6JC95bKpIiwi6Zu35Ye7Iiwi5rC05ryr6YeR5bGxIiwi5rOw5bGx5Y6L6aG2Iiwi5aWU6Zu35ZKSIiwi5Zyw54ux54OI54GrIiwi5q275Lqh5Y+s5ZSkIn0", "AQAAAAAAAAABAAAAQpsCAAAAAAD3mgIAAAAAAAAAAAAAAPC/"], [20, 1, "paste", null, "AQAAAPiaAgAAAAAAapsCAAAAAAAAAAAA", "AQAAAAAAAAABAAAA+JoCAAAAAAD4mgIAAAAAAAAAAAAAAPC/"], [23, 1, "insert", {"characters": "\t\t\t"}, "AwAAAPiaAgAAAAAA+ZoCAAAAAAAAAAAA+ZoCAAAAAAD6mgIAAAAAAAAAAAD6mgIAAAAAAPuaAgAAAAAAAAAAAA", "AQAAAAAAAAABAAAA+JoCAAAAAAD4mgIAAAAAAAAAAAAAAPC/"], [26, 1, "cut", {"event": {"modifier_keys": {}, "text_point": 170766, "x": 671.5, "y": 504.5}}, "AQAAAAabAgAAAAAABpsCAAAAAABaAAAAIuWWhOaBtuacieaKpSIsIuaDiuW/g+S4gOWJkSIsIuWjgeWekuWHu+egtCIsIuWKm+WKiOWNjuWxsSIsIuWJkeiNoeWbm+aWuSIsIuS4iuWPpOeBteespiIs", "AQAAAAAAAAABAAAABpsCAAAAAAAwmwIAAAAAAAAAAAAAAPC/"], [29, 1, "paste", null, "AQAAAEKbAgAAAAAAbJsCAAAAAAAAAAAA", "AQAAAAAAAAABAAAAQpsCAAAAAABCmwIAAAAAAAAAAAAAAPC/"], [32, 1, "insert", {"characters": ","}, "AQAAAEKbAgAAAAAAQ5sCAAAAAAAAAAAA", "AQAAAAAAAAABAAAAQpsCAAAAAABCmwIAAAAAAAAAAAAAAPC/"], [35, 1, "left_delete", null, "AQAAAGybAgAAAAAAbJsCAAAAAAABAAAALA", "AQAAAAAAAAABAAAAbZsCAAAAAABtmwIAAAAAAAAAAAAAAPC/"], [55, 1, "left_delete", null, "AQAAAO6aAgAAAAAA7poCAAAAAAAPAAAALCLll5zooYDov73lh7si", "AQAAAAAAAAABAAAA9ZoCAAAAAADumgIAAAAAAAAAAAAAAPC/"], [66, 1, "paste", null, "AQAAAMKaAgAAAAAAx5oCAAAAAAAAAAAA", "AQAAAAAAAAABAAAAwpoCAAAAAADCmgIAAAAAAAAAAAAAAPC/"], [69, 1, "insert", {"characters": "f"}, "AgAAAMOaAgAAAAAAxJoCAAAAAAAAAAAAxJoCAAAAAADEmgIAAAAAAAYAAADlkLjooYA", "AQAAAAAAAAABAAAAxZoCAAAAAADDmgIAAAAAAAAAAAAAAPC/"], [70, 1, "left_delete", null, "AQAAAMOaAgAAAAAAw5oCAAAAAAABAAAAZg", "AQAAAAAAAAABAAAAxJoCAAAAAADEmgIAAAAAAAAAAAAAAPC/"], [79, 1, "sequence", {"commands": []}, "BgAAAMOaAgAAAAAAxJoCAAAAAAAAAAAAw5oCAAAAAADFmgIAAAAAAAEAAABnw5oCAAAAAADGmgIAAAAAAAIAAABnYcOaAgAAAAAAyJoCAAAAAAADAAAAZ2Fvw5oCAAAAAADJmgIAAAAAAAUAAABnYW8nasOaAgAAAAAAxZoCAAAAAAAGAAAAZ2FvJ2pp", "AQAAAAAAAAABAAAAw5oCAAAAAADDmgIAAAAAAAAAAAAAAPC/"], [85, 1, "sequence", {"commands": []}, "BgAAAMWaAgAAAAAAxpoCAAAAAAAAAAAAxZoCAAAAAADHmgIAAAAAAAEAAABmxZoCAAAAAADImgIAAAAAAAIAAABmYcWaAgAAAAAAypoCAAAAAAADAAAAZmFuxZoCAAAAAADLmgIAAAAAAAUAAABmYW4nasWaAgAAAAAAx5oCAAAAAAAGAAAAZmFuJ2pp", "AQAAAAAAAAABAAAAxZoCAAAAAADFmgIAAAAAAAAAAAAAAPC/"], [94, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-08-01 12:31:54"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDctMzEgMjE6Mzc6MDk", "AQAAAAAAAAABAAAAt5oCAAAAAADAmgIAAAAAAAAAAAAAAPC/"]]}], "build_system": "Packages/Lua/ggeserver.sublime-build", "build_system_choices": [[[["Packages/Lua/ggebc.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggeobj.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], [[["Packages/Lua/ggeserver.sublime-build", ""], ["Packages/Lua/ggeserver.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "SetGGE"], ["Packages/Lua/ggeserver.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "AboutGGE"]], ["Packages/Lua/ggeserver.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "", "selected_items": [["i", "Package Control: Install Package"], ["lsp", "Package Control: List Packages"], ["Package Control: ", "Package Control: Add Channel"], ["install package", "Package Control: Install Package"], ["IN", "Package Control: Install Package"], ["in", "Package Control: Install Package"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 102.0, "history": ["import urllib.request, os, hashlib;", "import urllib.request, os, hashlib; "]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/服务端", "/D/BaiduNetdiskDownload/飞蛾静脉/gge"], "file_history": ["/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/神器类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/神器计算.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1001.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/道具处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/帮派商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家数据类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/系统处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/捉鬼任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/鬼王任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/自动抓鬼处理类.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/技能数据库.lua", "/D/myfwd/服务端/Script/系统处理类/ScriptInit.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/Script/数据中心/宝宝.lua", "/D/myfwd/服务端/Script/数据中心/变身卡.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/更新类/唱戏界面.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五庄观.lua", "/C/Users/<USER>/Desktop/最新7.30/服务端/<PERSON>ript/任务_小九/副本任务/五庄观.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/服务端/<PERSON>ript/工具/新区重置工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/成就处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/礼包奖励类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/剧情对话调用.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/9_飞升剧情.lua", "/D/myfwd/服务端/Script/战斗处理类/ScriptInit.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/9_渡劫剧情.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/2_商人的鬼魂.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/网络处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/临时处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/战神山.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派强盗.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/属性控制/宠物.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局循环类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/临时任务/首席争霸.lua", "/D/myfwd/服务端/Script/初始化脚本.lua", "/D/myfwd/服务端/Script/数据中心/ScriptInit.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话处理.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话内容.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/8_三打白骨精.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/怪物属性.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/队伍处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/角色处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器3.lua", "/D/修复备份/6.22最终/服务端/Script/助战处理类/MateControl.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/长安保卫战.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/无底洞.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/双城记.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五更寒.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/创建战斗.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局函数.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/装备处理类.lua", "/D/myfwd/服务端/Script/数据中心/物品数据.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/怪物调用/结算处理.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/帮派处理.lua", "/D/修复备份/6.22最终/服务端/Script/角色处理类/道具处理类.lua", "/D/myfwd/服务端/Script/任务_小九/小龟快跑.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/管理工具类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/神兵异兽榜.lua", "/D/myfwd/服务端/Script/数据中心/传送位置.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗固伤计算.lua", "/D/myfwd/服务端/Script/数据中心/场景NPC.lua", "/D/myfwd/服务端/Script/任务_小九/天降辰星.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/传送圈位置.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/AI战斗.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/神器任务/神归昆仑镜.lua", "/C/Users/<USER>/Desktop/7.20/服务端/Script/任务_小九/神器任务/神归昆仑镜.lua", "/D/修复备份/7.20/服务端/Script/任务_小九/神器任务/神归昆仑镜.lua", "/D/修复备份/7.13/服务端/Script/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/神器任务/墨魂笔之踪.lua", "/D/修复备份/7.13/服务端/Script/线程/定时器3.lua", "/D/修复备份/7.13/服务端/Script/线程/线程循环3.lua", "/D/修复备份/7.12/客户端/main.lua", "/D/修复备份/7.13/客户端/main.lua", "/D/修复备份/7.16/客户端/main.lua", "/C/Users/<USER>/Desktop/7.21/服务端/Script/任务_小九/神器任务/墨魂笔之踪.lua", "/C/Users/<USER>/Desktop/7.20/服务端/Script/任务_小九/神器任务/墨魂笔之踪.lua", "/C/Users/<USER>/Desktop/0715/服务端/Script/任务_小九/神器任务/墨魂笔之踪.lua", "/D/0.58/服务端/<PERSON>ript/对话处理类/NPC对话处理.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/雁塔试炼支线.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/乌鸡国.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/水陆大会.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/一斛珠.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa24420.10063.rartemp/五庄观.lua", "/C/Users/<USER>/Desktop/7.21/服务端/Script/剧情处理器/剧情对话调用.lua", "/D/myfwd/服务端/Script/任务_小九/王婆西瓜.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/队伍函数类.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/摆摊假人.lua", "/D/myfwd/服务端/main.lua", "/D/修复备份/7.20/服务端/main.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商城处理类.lua", "/D/myfwd/服务端/Script/数据中心/场景等级.lua", "/D/myfwd/服务端/Script/数据中心/明暗雷怪.lua", "/D/myfwd/服务端/Script/数据中心/染色.lua", "/D/myfwd/服务端/Script/数据中心/野怪.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/任务处理类.lua", "/D/myfwd/服务端/Script/数据中心/装备特技.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图坐标类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/地煞星.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/泾河龙王.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/抽奖处理.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/翰墨丹青.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/文韵墨香.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/师门任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派白虎.lua", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/对话处理类/对话调用/1815.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/雁塔地宫.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派迷宫.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统/物品掉落控制.lua", "/D/myfwd/服务端/Script/数据中心/场景名称.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/方法函数类.lua", "/C/Users/<USER>/Desktop/0715/服务端/Script/系统处理类/系统处理类.lua", "/D/myfwd/服务端/Script/数据中心/题库.lua", "/C/Users/<USER>/Desktop/0715/服务端/main.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8948.4815.rartemp/坐骑库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8948.7316.rartemp/技能库_新.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8948.3544.rartemp/战斗模型库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8948.40226.rartemp/战斗模型库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8948.32316.rartemp/main.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8788.26303.rartemp/头像库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8788.27281.rartemp/特效库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8788.22830.rartemp/战斗模型库.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa8788.25002.rartemp/自定义库.lua", "/D/myfwd/服务端/<PERSON>ript/地图处理类/MAP.lua"], "find": {"height": 44.0}, "find_in_files": {"height": 127.0, "where_history": ["D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端\\Script\\任务_小九,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端\\Script\\任务_小九,<project filters>", "D:\\myfwd\\服务端,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端", "D:\\myfwd\\Server\\Script,<project filters>", "D:\\myfwd\\Server,<project filters>", "D:\\myfwd\\Server\\Script,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\客户端,C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\0.543\\服务端", "D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端\\Script", "D:\\【花好月圆】-三经脉版本-助战分角色+VIP礼包+会员卡+剧情活动\\服务端\\Script\\战斗处理类", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端\\源码备份\\服务端\\Script", "D:\\梦幻服务端\\服务端", "C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script", "D:\\死神互通全套源码\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端", "D:\\梦幻服务端\\服务端\\Script", "for i=2,5  do", "D:\\梦幻服务端\\服务端\\Script", "D:\\花好互通\\fuwudd\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端,D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端", "D:\\新建文件夹 (3)\\服务端源码", "D:\\梦幻服务端\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\数据中心", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\周末玩法", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\角色处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\副本任务", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script"]}, "find_state": {"case_sensitive": false, "find_history": ["追加法术", "点化", "bb套", "吸附石", "伤害", "神器", "给予跑商道具", "跑商", "拆分", "自动抓鬼", "序号==29", "坐骑技能", "开辟", "劈滴", "挨打", "变身卡数据", "妙手", "龙腾", "妙手空空", "进阶", "进阶蝴蝶", "目标=自动栏[3]", "变身卡数据", "变身卡", "妙手空空", "需要幻化", "幻化", "原造型", "妙手空空偷取执行", "妙手空", "妙手", "循环", "全局循环", "踢", "成就系统", "剧情奖励100", "飞升剧情", "礼包奖励", "天罗", "9_渡劫剧情", "剧情奖励100", "剧情奖励85(id)", "生死劫", "剧情奖励20", "剧情奖励80", "剧情奖励85", "剧情奖励80", "剧情奖励70", "剧情奖励50", "剧情奖励30", "成就处理", "临时处理", "成就", "进度", "判断进度", "成就数据[v]:判断进度", "商人的鬼魂", "捕捉计算", "成就处理类", "捉鬼", "抓鬼", "存档", "已为帮派", "生肖属性", "重置经验", "副本次数", "重置副本", "重置经验", "回收", "管理命令", "次数", "鬼王", "妖王", "300", "次数", "保存任务数据()", "存档", "cy", "copyTable", "table.tostring", "生肖", "成就", "已为帮派", "以为帮派", "四不像", "三界", "三界书院题库", "8_飞升剧情", "我的召唤兽受伤了", "超级巫医", "吸收", "盘丝", "盘地洞", "取玩家装备信息", "self.装备[3]", "@bcrw", "table.loadstring", "创建队伍", "table.copy", "加载任务数据", "断开游戏", "队伍处理", "1050", "队伍处理", "超级巫医", "超级五一", "高级藏宝图", "local 链接", "链接={提示=format(", "聊天处理", "树妖", "败了暗雷树妖，但没有获得特", "暗雷", "乱灵", "1000", "现在不是", "现在", "714", "7104", "树妖", "乱灵", "160014", "110064", "110062", "树妖", "混乱的树妖", "青莲仙女", "观音菩萨"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["160014", "local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)", "金铙僧", "超级神牛(辛丑)", "超级神虎(壬寅)", "7103", "属性.伤害 * 1.1", "属性.伤害 * 1.05", "属性.法伤", "属性.速度", "幽萤娃娃", "0xaaddee99", "去寻找明月", "160011", "160010", "告辞", "古董货商", "虞悄悄", "", ",法防 = math.floor(等级*7*难度系数*0.8)", ",防御 = math.floor(等级*7*难度系数)", "\t,防御 = math.floor(等级*5*难度系数)\n\t,法防 = math.floor(等级*5*难度系数)", "藏宝图", "", "破碎无双", "\"高级魔兽要诀\"", "", "6100", "2008", "2001", "7500", "1625", "3000", "8020", "1005", ",穿刺等级= 穿刺等级", ",穿刺等级= 计算穿刺等级", ",穿刺等级= 300", "战斗", "速度 = math.floor(sx.属性.速度*难度系数),", "法伤 = math.floor(sx.属性.法伤*难度系数),", "法伤 = math.floor(sx.属性.速度*难度系数),", "伤害 = math.floor(sx.属性.速度*难度系数),", "伤害 = math.floor(sx.属性.法伤*难度系数),", "伤害 = math.floor(sx.属性.伤害*难度系数),", "物抗=xiulian + 3", "物抗=xiulian + 5 ", "", "伤害=等级*18", "法防=等级*9", "防御=等级*11", ",技能={\"高级感知\",\"高级反震\",\"高级防御\",\"高级反震\",\"高级魔之心\",\"高级法术波动\",\"高级敏捷\",\"高级强力\",\"高级夜战\",\"高级偷袭\",\"高级必杀\"}", ",气血=等级*等级*8", ",速度=等级*6", ",技能={\"高级感知\"}", "", "法伤结果", "临时法伤结果", "1-目标数*0.1", "计算固伤附加属性", "附加属性.灵饰伤害+附加属性.符石伤害+附加属性.拭剑石", "qz(fhz*分灵+修炼差*5+神木符)", ")", "(", "战斗数据.参战单位[编号].拭剑石伤害", "战斗数据:取灵饰属性", "战斗数据:取符石属性", "固定伤害结果", "宝石属性", "", "local 武器伤害 = math.floor(战斗数据.参战单位[编号].装备伤害)", "+ 4000", "local 名称 = \"特赦令牌\"", "高级藏宝图", "local 经验 = 等级 * 取随机数(120, 130)", "local 银子 = 等级 * 取随机数(105, 120)", "local 经验 = 等级 * 取随机数(140, 160)", "特效宝珠\n\t", "\n\t", "特效宝珠", "躲避", ",速度 = math.floor(sx.属性.速度*1.5)", "取随机数(95, 105)", "等级 * 12", "速度=等级*4", ",速度=等级*3.5", ",伤害=等级*15", "取随机数(90,120)", "取随机数(140,145)", "达到", "被封印 >= 3", "铁血", "渡劫", "奖励参数", "防御 = math.floor(等级*13)", "法防 = math.floor(等级*11)", "速度 = qz(sx.属性.速度)", "", "11", "玩家数据[v].道具", "玩家数据", "玩家数据[v].道具:给予道具", "玩家数据[v].道具:", "主线=10", "self:下一页", "self:第一页", "法伤=等级*13", "sx.属性.气血*20", "sx.属性.气血*16", "气血=等级*145", "气血=等级*125", "气血=等级*140", "sx.属性.气血*10", "sx.属性.气血*12", "法伤=等级*9", "“同气”", "“淝水之战”", "“同气”", "self:取角色选择信息(id, 123)", "发送数据(玩家数据[id].连接id, 122", "发送数据(玩家数据[id].连接id, 121)", "self:取角色选择信息(id, 120)", "降妖伏魔:完成", "self", "任务id", "任务数据[任务id]", "local 银子=等级*55+取随机数(200,400)", "local 经验=等级*取随机数(115,125)"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 4251, "regions": {}, "selection": [[2277, 2277]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 7, 21, 0, 4, 12, 5, 118, 119, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 1742.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "semi_transient": false, "settings": {"buffer_size": 15299, "regions": {"match": {"flags": 112, "regions": "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", "scope": ""}}, "selection": [[14141, 14141]], "settings": {"detect_indentation": false, "line_numbers": false, "output_tag": 3, "result_base_dir": "", "result_file_regex": "^([^ \t].*):$", "result_line_regex": "^ +([0-9]+):", "scroll_past_end": true, "syntax": "Packages/Default/Find Results.hidden-tmLanguage"}, "translation.x": 0.0, "translation.y": 9832.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "Script/对话处理类/对话调用/1001.lua", "semi_transient": false, "settings": {"buffer_size": 62478, "regions": {}, "selection": [[45425, 45425]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 19, 48, 13, 127, 94, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 31032.0, "zoom_level": 1.0}, "stack_index": 3, "stack_multiselect": false, "type": "text"}, {"buffer": 3, "file": "Script/对话处理类/NPC对话处理.lua", "semi_transient": false, "settings": {"buffer_size": 30053, "regions": {}, "selection": [[25672, 25672]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 19, 48, 13, 74, 156, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 17998.0, "zoom_level": 1.0}, "stack_index": 4, "stack_multiselect": false, "type": "text"}, {"buffer": 4, "file": "Script/战斗处理类/战斗处理.lua", "semi_transient": false, "settings": {"buffer_size": 463043, "regions": {}, "selection": [[95139, 95139]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 19, 49, 1, 247, 91, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 36525.0, "zoom_level": 1.0}, "stack_index": 5, "stack_multiselect": false, "type": "text"}, {"buffer": 5, "file": "Script/角色处理类/召唤兽处理类.lua", "semi_transient": false, "settings": {"buffer_size": 47235, "regions": {}, "selection": [[46291, 45981]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 19, 49, 3, 190, 123, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 2, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 40672.0, "zoom_level": 1.0}, "stack_index": 6, "stack_multiselect": false, "type": "text"}, {"buffer": 6, "file": "Script/角色处理类/道具处理类.lua", "selected": true, "semi_transient": false, "settings": {"buffer_size": 272797, "regions": {}, "selection": [[170861, 170861]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 8, 1, 11, 19, 49, 3, 252, 251, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 141354.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 32.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 156.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.unsaved_changes": {"height": 277.0}, "pinned_build_system": "Packages/Lua/ggeserver.sublime-build", "project": "开发服务端.sublime-project", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["", "服务端\\Script\\战斗处理类\\ScriptInit.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 231.0, "last_filter": "", "selected_items": [], "width": 574.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 222.0, "status_bar_visible": true, "template_settings": {}}